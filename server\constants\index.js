export const rolesValues = {
	SuperAdmin: 1,
	Admin: 2,
	CommunityMember: 3,
};

export const rolesValuesNumToStr = {
	1: "SuperAdmin",
	2: "Admin",
	3: "CommunityMember",
};

export const videoTypeStrToNum = {
	EarlyLife: 1,
	ProfessionalLife: 2,
	CurrentLife: 3,
};

export const videoTypeNumToStr = {
	1: "EarlyLife",
	2: "ProfessionalLife",
	3: "CurrentLife",
};

export const videoTypeWithSpacing = {
	EarlyLife: "Early Life",
	ProfessionalLife: "Professional Life",
	CurrentLife: "Current Life",
};

export const USER_FIELD_MAP = {
	[videoTypeNumToStr[1]]: {
		onboardingLifeData: "earlyLifeData",
		onboardingUpdatedLifeData: "updatedEarlyLifeData",
	},
	[videoTypeNumToStr[2]]: {
		onboardingLifeData: "professionalLifeData",
		onboardingUpdatedLifeData: "updatedProfessionalLifeData",
	},
	[videoTypeNumToStr[3]]: {
		onboardingLifeData: "currentLifeData",
		onboardingUpdatedLifeData: "updatedCurrentLifeData",
	},
};

export const ONBOARDING_STEP = {
	BASIC_DETAILS: 1,
	EARLY_LIFE_VIDEO: 2,
	EARLY_LIFE_FORM: 3,
	PROFESSIONAL_LIFE_VIDEO: 4,
	PROFESSIONAL_LIFE_FORM: 5,
	CURRENT_LIFE_VIDEO: 6,
	CURRENT_LIFE_FORM: 7,
	FINAL_SUBMIT: 8,
	WAIT_FOR_APPROVAL: 9,
	COMPLETED: 10,
};

export const MAX_VIDEO_PROCESS_RETRY_COUNT = 5;
