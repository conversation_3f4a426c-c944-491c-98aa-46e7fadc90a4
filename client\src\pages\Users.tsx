import { useCallback, useEffect, useState } from "react";
import { apiClient } from "../config/axios";
import {
	Container,
	Title,
	Divider,
	Space,
	Group,
	Button,
	TextInput,
	Loader,
	Center,
	Pagination,
	Text,
	NativeSelect,
	Paper,
	Stack,
} from "@mantine/core";
import CreateUserForm from "../components/CreateUserForm";
import UserTable from "../components/UserTable";
import { notifications } from "@mantine/notifications";
import {
	IconX,
	IconUserPlus,
	IconCheck,
	IconSearch,
	IconSortAscending,
} from "@tabler/icons-react";
import type { UserCreation } from "../types";
import { useAuth } from "../contexts/AuthContext";
import { Modal } from "@mantine/core";
import { useDebouncedValue } from "@mantine/hooks";
import { DEBOUNCE_TIME_IN_MS } from "../constants";

const Users = () => {
	const [users, setUsers] = useState<UserCreation[]>([]);
	const [page, setPage] = useState(1);
	const [totalPages, setTotalPages] = useState(1);
	const [loading, setLoading] = useState(true);
	const { user } = useAuth();
	const currentUserRole: number = user?.role ?? 0;
	const [opened, setOpened] = useState(false);
	const [userToEdit, setUserToEdit] = useState<UserCreation | null>(null);
	const [searchQuery, setSearchQuery] = useState("");
	const [limit, setLimit] = useState(5);
	const [totalUsers, setTotalUsers] = useState(0);
	const start = (page - 1) * limit + 1;
	const end = Math.min(page * limit, totalUsers);
	const [sortBy, setSortBy] = useState("firstName");
	const [debouncedSearch] = useDebouncedValue(
		searchQuery,
		DEBOUNCE_TIME_IN_MS
	);

	const fetchUsers = useCallback(
		async (currentPage: number) => {
			setLoading(true);
			try {
				const res = await apiClient.get(
					`/api/users?page=${currentPage}&limit=${limit}&sort=${sortBy}&search=${encodeURIComponent(debouncedSearch.trim())}`
				);

				setUsers(res.data.data);
				setTotalPages(res.data.totalPages);
				setTotalUsers(res.data.total);
			} catch (err) {
				console.error("Error fetching users:", err);
				notifications.show({
					title: "Error",
					message: "Failed to fetch users",
					color: "red",
					icon: <IconX />,
				});
			} finally {
				setLoading(false);
			}
		},
		[limit, sortBy, debouncedSearch]
	);

	useEffect(() => {
		fetchUsers(page);
	}, [page, fetchUsers]);

	const handleUserCreated = (newUser: UserCreation) => {
		setUsers(prevUsers => [...prevUsers, newUser]);
		fetchUsers(page);
	};

	const handleUserUpdated = (updatedUser: UserCreation) => {
		setUsers(prevUsers =>
			prevUsers.map(user =>
				user._id === updatedUser._id ? updatedUser : user
			)
		);
		setUserToEdit(null);
	};

	const handleEditUser = (user: UserCreation) => {
		setUserToEdit(user);
		setOpened(true);
	};

	const handleDeleteUser = async (userId: string) => {
		try {
			const res = await apiClient.delete(`/api/users/delete/${userId}`);
			setUsers(prev => prev.filter(u => u._id !== userId));
			notifications.show({
				title: "User Deleted",
				message: res.data.message,
				color: "green",
				icon: <IconCheck />,
			});
			fetchUsers(page);
		} catch {
			notifications.show({
				title: "Failed",
				message: "Failed to delete user",
				color: "red",
				icon: <IconX />,
			});
		}
	};

	const getAllowedRoles = (): (
		| "SuperAdmin"
		| "Admin"
		| "CommunityMember"
	)[] => {
		if (currentUserRole === 1) {
			return ["SuperAdmin", "Admin", "CommunityMember"];
		}
		if (currentUserRole === 2) {
			return ["CommunityMember"];
		}
		return [];
	};

	return (
		<Container>
			<Group justify="space-between">
				<Title order={1}>Users</Title>
				{getAllowedRoles().length > 0 && (
					<Button
						leftSection={<IconUserPlus size={14} />}
						onClick={() => setOpened(true)}
					>
						Create User
					</Button>
				)}
			</Group>
			<Divider my="md" />

			{getAllowedRoles().length > 0 && (
				<>
					<Modal
						opened={opened}
						onClose={() => {
							setOpened(false);
							setUserToEdit(null);
						}}
						title={userToEdit ? "Edit User" : "Create User"}
						size="lg"
						centered
					>
						<CreateUserForm
							allowedRoles={getAllowedRoles()}
							onUserCreated={
								userToEdit
									? handleUserUpdated
									: handleUserCreated
							}
							setOpened={setOpened}
							userToEdit={userToEdit}
						/>
						<Space h="md" />
					</Modal>
				</>
			)}

			<Paper withBorder p="md" mb="md">
				<Stack gap={6}>
					<Group justify="space-between" align="center">
						<Group align="center" gap="sm">
							<TextInput
								leftSection={<IconSearch size={16} />}
								placeholder="Search by name or email"
								value={searchQuery}
								w={300}
								onChange={e => {
									setSearchQuery(e.target.value);
									setPage(1);
								}}
								size="sm"
							/>
							<NativeSelect
								leftSection={<IconSortAscending size={14} />}
								w={180}
								value={sortBy}
								onChange={e => {
									setSortBy(e.target.value);
									setPage(1);
								}}
								data={[
									{ label: "First Name", value: "firstName" },
									{
										label: "Last Name",
										value: "secondName",
									},
									{ label: "Email", value: "email" },
								]}
								size="sm"
							/>
						</Group>

						<NativeSelect
							w={180}
							value={String(limit)}
							onChange={e => {
								setLimit(Number(e.target.value));
								setPage(1);
							}}
							data={[
								{ label: "5 per page", value: "5" },
								{ label: "10 per page", value: "10" },
								{ label: "20 per page", value: "20" },
							]}
							size="sm"
						/>
					</Group>

					<Text c="gray.7" size="sm">
						Showing {start}-{end} of {totalUsers} users
					</Text>
				</Stack>
			</Paper>

			<div>
				{loading ? (
					<Center>
						<Loader />
					</Center>
				) : (
					<>
						<UserTable
							users={users}
							currentUserRole={currentUserRole}
							onDelete={handleDeleteUser}
							onEdit={handleEditUser}
						/>

						<Group justify="flex-end">
							<Pagination
								total={totalPages}
								value={page}
								onChange={setPage}
								mt="md"
								siblings={1}
								boundaries={1}
							/>
						</Group>
					</>
				)}
			</div>
		</Container>
	);
};

export default Users;
