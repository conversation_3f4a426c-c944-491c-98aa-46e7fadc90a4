import {
	Card,
	Text,
	Group,
	Stack,
	Badge,
	Avatar,
	Box,
	Anchor,
	SimpleGrid,
	rem,
	Flex,
} from "@mantine/core";
import {
	IconHeart,
	IconQuote,
	IconMapPin,
	IconMail,
	IconBrandTwitter,
	IconBrandInstagram,
	IconBrandLinkedin,
	IconNotebook,
	IconWorld,
} from "@tabler/icons-react";
import type { ReactNode } from "react";
import { roleLabels } from "../constants";
import { resolveImageUrl } from "../utils/imageUrl";

type UserProfileCardProps = {
	firstName: string;
	middleName: string;
	lastName: string;
	email: string;
	role: number;
	image: string;
	address: string;
	city: string;
	introduction: string;
	quote: string;
	joy: string;
	contentLinks: string[];
	currentOrganization: string;
	twitter: string;
	instagram: string;
	linkedIn: string;
	otherSocialHandles: string[];
};

type InfoFieldProps = {
	icon: ReactNode;
	label?: string;
	value: string;
	isLink?: boolean;
};

const InfoItem = ({ icon, label, value, isLink = false }: InfoFieldProps) => {
	const displayValue = value || "N/A";
	return (
		<Group
			align="flex-start"
			gap="sm"
			wrap="nowrap"
			p="sm"
			style={{ backgroundColor: "#f9fafb", borderRadius: rem(8) }}
		>
			<Box mt={2} color="blue.6">
				{icon}
			</Box>
			<Stack gap={2} style={{ flex: 1 }}>
				<Text size="xs" tt="uppercase" c="gray.6" fw={500}>
					{label}
				</Text>
				{isLink && value ? (
					<Anchor
						href={value}
						target="_blank"
						c="blue.7"
						size="sm"
						style={{ wordBreak: "break-word" }}
					>
						{displayValue}
					</Anchor>
				) : (
					<Text size="sm" c="gray.9">
						{displayValue}
					</Text>
				)}
			</Stack>
		</Group>
	);
};

const UserProfileCard = ({
	firstName,
	middleName,
	lastName,
	email,
	role,
	image,
	address,
	city,
	introduction,
	quote,
	joy,
	contentLinks,
	currentOrganization,
	twitter,
	instagram,
	linkedIn,
	otherSocialHandles = [],
}: UserProfileCardProps) => {
	return (
		<>
			<SimpleGrid
				cols={{ base: 1, lg: 3 }}
				spacing="lg"
				style={{ alignItems: "start" }}
			>
				<Stack gap="lg" style={{ gridColumn: "span 2" }}>
					<Card withBorder shadow="sm" radius="md">
						<Stack gap="sm">
							<Group>
								<Text fw={600}>About</Text>
							</Group>
							<InfoItem
								icon={<IconNotebook size={18} />}
								label="Introduction"
								value={introduction}
							/>
							<InfoItem
								icon={<IconMapPin size={18} />}
								label="City"
								value={city}
							/>
							<InfoItem
								icon={<IconMapPin size={18} />}
								label="Address"
								value={address}
							/>
							<InfoItem
								icon={<IconHeart size={18} />}
								label="What brings me joy"
								value={joy}
							/>
						</Stack>
					</Card>

					{(email || twitter || instagram || linkedIn) && (
						<Card withBorder shadow="sm" radius="md">
							<Stack gap="sm">
								<Group>
									<Text fw={600}>Contact & Social</Text>
								</Group>
								<InfoItem
									icon={<IconMail size={18} />}
									label="Email"
									value={email}
								/>
								<InfoItem
									icon={<IconBrandTwitter size={18} />}
									label="Twitter"
									value={twitter}
									isLink
								/>
								<InfoItem
									icon={<IconBrandInstagram size={18} />}
									label="Instagram"
									value={instagram}
									isLink
								/>
								<InfoItem
									icon={<IconBrandLinkedin size={18} />}
									label="LinkedIn"
									value={linkedIn}
									isLink
								/>
							</Stack>
						</Card>
					)}

					{contentLinks?.length > 0 && (
						<Card withBorder shadow="sm" radius="md">
							<Stack gap="sm">
								<Group>
									<Text fw={600}>Content Links</Text>
								</Group>
								{contentLinks.map((link, i) => (
									<InfoItem
										key={i}
										icon={<IconWorld size={18} />}
										value={link}
										isLink
										label={undefined}
									/>
								))}
							</Stack>
						</Card>
					)}
					{otherSocialHandles?.length > 0 && (
						<Card withBorder shadow="sm" radius="md">
							<Stack gap="sm">
								<Group>
									<Text fw={600}>Other Social Handles</Text>
								</Group>
								{otherSocialHandles.map((link, i) => (
									<InfoItem
										key={i}
										icon={<IconWorld size={18} />}
										value={link}
										isLink
										label={undefined}
									/>
								))}
							</Stack>
						</Card>
					)}
				</Stack>

				<Box
					style={{
						position: "sticky",
						top: 80,
						alignSelf: "start",
					}}
				>
					<Card withBorder shadow="sm" radius="md">
						<Stack align="center" p="lg">
							<Box pos="relative">
								<Avatar
									src={resolveImageUrl(image)}
									size={128}
									radius="xl"
									style={{ border: "4px solid #dbeafe" }}
								>
									{firstName[0]}
								</Avatar>
							</Box>

							<Text size="xl" fw={700}>
								{firstName} {middleName} {lastName}
							</Text>
							{currentOrganization && (
								<Text c="gray.7">{currentOrganization}</Text>
							)}
							{role && (
								<Badge
									p="md"
									bdrs="md"
									variant="light"
									color="blue"
									radius="sm"
								>
									{
										roleLabels[
											role as keyof typeof roleLabels
										]
									}
								</Badge>
							)}

							{quote && (
								<Box
									mt="md"
									p="md"
									style={{
										backgroundColor: "#f9fafb",
										borderLeft: `4px solid #3b82f6`,
										borderRadius: rem(6),
										width: "300px",
									}}
								>
									<IconQuote
										size={16}
										color="blue"
										style={{ marginBottom: 8 }}
									/>
									<Flex justify={"center"}>
										<Text size="sm" fs={"italic"}>
											{quote}
										</Text>
									</Flex>
								</Box>
							)}
						</Stack>
					</Card>
				</Box>
			</SimpleGrid>
		</>
	);
};

export default UserProfileCard;
