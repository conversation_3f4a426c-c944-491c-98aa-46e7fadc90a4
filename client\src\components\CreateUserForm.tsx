import { useState } from "react";
import { TextInput, But<PERSON>, Select, Stack } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { <PERSON>con<PERSON>he<PERSON>, IconX } from "@tabler/icons-react";
import { apiClient } from "../config/axios";
import { roleValues } from "../constants";
import { useForm } from "@mantine/form";
import { rolesLabelMap } from "../constants";

interface Props {
	allowedRoles: ("SuperAdmin" | "Admin" | "CommunityMember")[];
	onUserCreated: (user: {
		_id: string;
		firstName: string;
		middleName?: string;
		secondName: string;
		email: string;
		mobile: string;
		role: number;
	}) => void;
	userToEdit?: {
		_id: string;
		firstName: string;
		middleName?: string;
		secondName: string;
		email: string;
		mobile: string;
		role: number;
	} | null;
	setOpened: (opened: boolean) => void;
}

const CreateUserForm = ({
	allowedRoles,
	onUserCreated,
	setOpened,
	userToEdit,
}: Props) => {
	const [loading, setLoading] = useState(false);

	const form = useForm({
		initialValues: {
			firstName: userToEdit?.firstName || "",
			middleName: userToEdit?.middleName || "",
			secondName: userToEdit?.secondName || "",
			email: userToEdit?.email || "",
			mobile: userToEdit?.mobile || "+91",
			role: userToEdit
				? Object.keys(roleValues).find(
						key =>
							roleValues[key as keyof typeof roleValues] ===
							userToEdit.role
					) || ""
				: "",
		},
		transformValues: values => ({
			...values,
			firstName: values.firstName.trim(),
			middleName: values.middleName?.trim(),
			secondName: values.secondName.trim(),
			email: values.email.toLowerCase().trim(),
		}),
		validate: {
			firstName: val =>
				val.trim() === "" ? "First name is required" : null,
			secondName: val =>
				val.trim() === "" ? "Last name is required" : null,
			email: val => {
				const trimmedVal = val.trim();
				if (!val) {
					return "Email is required";
				}
				if (!/^\S+@\S+\.\S+$/.test(trimmedVal)) {
					return "Invalid email address";
				}
				return null;
			},
			mobile: val =>
				/^\+\d{1,3}\d{4,14}$/.test(val)
					? null
					: "Enter a valid mobile number with country code (e.g., +14155552671 or +919876543210)",
			role: val => (!val ? "Please select a role" : null),
		},
	});

	const handleSubmit = async (values: typeof form.values) => {
		setLoading(true);
		try {
			const roleValue =
				roleValues[values.role as keyof typeof roleValues];

			let res;
			if (userToEdit) {
				res = await apiClient.put(
					`/api/users/update-user/${userToEdit._id}`,
					{
						firstName: values.firstName,
						middleName: values.middleName,
						secondName: values.secondName,
					}
				);
				notifications.show({
					title: "Success",
					message: "User updated successfully!",
					color: "green",
					icon: <IconCheck />,
				});
			} else {
				res = await apiClient.post("/api/users/create-user", {
					firstName: values.firstName,
					middleName: values.middleName,
					secondName: values.secondName,
					email: values.email,
					mobile: values.mobile,
					role: roleValue,
				});
				notifications.show({
					title: "Success",
					message: "User created successfully!",
					color: "green",
					icon: <IconCheck />,
				});
			}

			if (onUserCreated) onUserCreated(res.data);
			form.reset();
			setOpened(false);
		} catch (err: any) {
			console.error(
				userToEdit ? "Error updating user: " : "Error creating user: ",
				err
			);
			const errorMessage =
				err.response?.data?.message ||
				(userToEdit
					? "Something went wrong while updating the user."
					: "Something went wrong while creating the user.");

			notifications.show({
				title: "Error",
				message: errorMessage,
				color: "red",
				icon: <IconX />,
			});
		}
		setLoading(false);
	};

	return (
		<form onSubmit={form.onSubmit(handleSubmit)}>
			<Stack>
				<TextInput
					label="First Name"
					placeholder="Enter first name"
					{...form.getInputProps("firstName")}
					required
				/>
				<TextInput
					label="Middle Name"
					placeholder="Enter middle name"
					{...form.getInputProps("middleName")}
				/>
				<TextInput
					label="Last Name"
					placeholder="Enter last name"
					{...form.getInputProps("secondName")}
					required
				/>
				<TextInput
					label="Email"
					placeholder="Enter email"
					{...form.getInputProps("email")}
					disabled={!!userToEdit}
					required
				/>
				<TextInput
					label="Mobile"
					placeholder="Enter Phone number"
					{...form.getInputProps("mobile")}
					disabled={!!userToEdit}
					required
				/>
				<Select
					label="Role"
					placeholder="Select role"
					data={allowedRoles.map(r => ({
						value: r,
						label: rolesLabelMap[r],
					}))}
					{...form.getInputProps("role")}
					required
					disabled={!!userToEdit}
				/>
				<Button type="submit" loading={loading}>
					{userToEdit ? "Update User" : "Create User"}
				</Button>
			</Stack>
		</form>
	);
};

export default CreateUserForm;
