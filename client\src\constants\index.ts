import type { profileStatusDataType } from "../types";

export const roleLabels = {
	1: "SuperAdmin",
	2: "Admin",
	3: "CommunityMember",
} as const;

export const rolesLabelMap = {
	SuperAdmin: "Super Admin",
	Admin: "Admin",
	CommunityMember: "Community Member",
};

export const roleValues = {
	SuperAdmin: 1,
	Admin: 2,
	CommunityMember: 3,
} as const;

export const allowedRoles = ["SuperAdmin", "Admin", "CommunityMember"];

export const MAX_RECORDING_TIME = 15 * 60;

export const videoTypeMap = {
	"1": "Early Life",
	"2": "Professional Life",
	"3": "Current Life",
};

export const videoTypeLabel = {
	EarlyLife: "Early Life",
	ProfessionalLife: "Professional Life",
	CurrentLife: "Current Life",
};

export const VIDEO_UPLOAD_MAX_SIZE_IN_MB = 2000;
export const IMAGE_UPLOAD_MAX_SIZE_IN_MB = 2;

export const profileStatusColorMap: Record<profileStatusDataType, string> = {
	pending: "yellow",
	approved: "green",
	"re-approved": "orange",
};

export const ONBOARDING_STEP = {
	BASIC_DETAILS: 1,
	EARLY_LIFE_VIDEO: 2,
	EARLY_LIFE_FORM: 3,
	PROFESSIONAL_LIFE_VIDEO: 4,
	PROFESSIONAL_LIFE_FORM: 5,
	CURRENT_LIFE_VIDEO: 6,
	CURRENT_LIFE_FORM: 7,
	FINAL_SUBMIT: 8,
	WAIT_FOR_APPROVAL: 9,
	COMPLETED: 10,
};

export const DEBOUNCE_TIME_IN_MS = 500;
