# ChangeLog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.1.0/)

## Unreleased
## [1.0.2]

### Fixed: Smooth Scroll for Input Errors in Basic Details [[#52](https://github.com/CQ-Dev-Team/360/issues/52)]

- Now when there is an error in an input field in basic details, the user gets to know which input field is giving error due to smooth scroll.

### Fixed: Search on user listing and pending approvals [[#48](https://github.com/CQ-Dev-Team/360/issues/48)]

- Ensured search input trimming and implemented debounced search 

## Released
## [1.0.1]

## AppLayout Navigation

- **Added** `NavItemsDataType` with `navigation` and `divider` types.
- **Refactored** `navItems` to use `useMemo` instead of push-based logic.
- **Introduced** `showIf` boolean for conditional rendering.
- **Added** `"Admin Controls"` divider for non-community members.
- **Removed** `user?.canApproveUser` check (previously gated `Pending Profiles`) that is not needed.

### Updated Professional Life Video Instruction

- ### Before

- - Different jobs, roles, or positions you’ve held – including internships, employment, entrepreneurship, or freelancing.
- - Cover all career experiences except what you are doing right now.

- ### After

- - Talk about the different jobs or roles you’ve had in the past (like intern, employee, founder, owner, or freelancer).
- - Be sure to mention your titles too (for example: analyst, manager, director). Don’t include your current role—focus only on your past experiences.

### Update UI and OpenAI prompt for Professional Life Video

- Now Instead of seprate field for roles, we have a single field as tags in roles.
- Change the prompt for openAI to extract data from the video. (Added new Line in Prompt: When extracting roles, normalize them into valid professional job titles.Ensure roles sound like proper job designations (Analyst, Manager, Associate, Specialist, Executive, etc.) rather than just functional areas.)

## [1.0.0]

### Added

- Add admin panel functionality.
- - Implemented admin panel view for SuperAdmin and Admin users.
- - Added isAdminPanelUser field to User model and updated related routes and middleware.
- - Added checkAdminPanelView middleware to check if user is allowed to change admin panel view.
- - Created changeAdminPanelView controller to toggle admin panel access.
- - Added button in header to change admin panel view and switch back to onboarding view if user is not on onboarding step. conditionally show/hide based on user role.
- - Profile Life Data tabs are hidden if user user is on admin panel view.

### Added: Add video recording and instruction components with framer-motion integration

- Implemented VideoInstructionsModal and Instruction components for video guidance (make it reuseable).
- Created InstructionData to manage instructional content based on video type.
- Change file location of RecordVideoModal and VideoRecorder for video recording functionality.
- Integrated framer-motion for smooth transitions between recording states.
- Updated package.json and package-lock.json to include framer-motion dependency.

### Added : The recorded/uploaded video will be part of your profile and will be visible to other community members in video upload when video is selected / recorded

### Updated UI Message After Video Upload

- ### Before

- - Thanks for uploading your video We're hard at work processing them to create your personalized experience. This might take a few moments.
- - Once the video is processed and data is extracted, we will notify you via email.

- ### After

- - Thanks for uploading your video! We're processing it to create your personalized experience.
- - This usually takes 1–2 minutes. You’ll be automatically redirected to next stage once it’s ready, and we’ll also notify you by email.

### Fixed

### Updated Global Search for user

- Earlier user will search on the basis of first name and last name
- Now user can search on the basis of first name, middle name, last name, email, early life tags, professional life tags, current life tags
  
- Issue[[#45](https://github.com/CQ-Dev-Team/360/issues/45)] update file upload paths to support image uploads and migrate existing video paths

### Removed
