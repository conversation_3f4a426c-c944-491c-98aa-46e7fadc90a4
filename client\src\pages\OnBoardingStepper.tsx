import { Step<PERSON>, <PERSON>, Modal, <PERSON><PERSON>, Group, Text } from "@mantine/core";
import { useEffect, useState, useRef, useCallback, useTransition } from "react";
import EarlyLifeForm from "../components/onBoarding-forms/EarlyLifeForm";
import ProfessionalLifeForm from "../components/onBoarding-forms/ProfessionalLifeForm";
import CurrentLifeForm from "../components/onBoarding-forms/CurrentLifeForm";
import apiClient from "../config/axios";
import type { AllLifeDataType } from "../types";
import { useNavigate, useParams } from "react-router-dom";
import FullScreenLoader from "../components/FullScreenLoader";
import VideoUpload from "../components/VideoUpload";
import { useAuth } from "../contexts/AuthContext";
import Profile from "./Profile";
import { IconCheck } from "@tabler/icons-react";
import ProfileTabsWrapper from "../components/ProfileTabsWrapper";

type StepsType = {
	label: string;
	description: string;
	param: string;
};

const STEPS: StepsType[] = [
	{
		label: "Step1",
		description: "Basic Details",
		param: "basic-details",
	},
	{
		label: "Step2",
		description: "Early Life Video",
		param: "earlyLifeVideo",
	},
	{
		label: "Step3",
		description: "Early Life Details",
		param: "earlyLifeDetails",
	},
	{
		label: "Step4",
		description: "Professional Life Video",
		param: "professionalLifeVideo",
	},
	{
		label: "Step5",
		description: "Professional Life Details",
		param: "professionalLifeDetails",
	},
	{
		label: "Step6",
		description: "Current Life Video",
		param: "currentLifeVideo",
	},
	{
		label: "Step7",
		description: "Current Life Details",
		param: "currentLifeDetails",
	},
	{
		label: "Step8",
		description: "Sumbit for Review",
		param: "finalStep",
	},
];

function OnBoardingStepper() {
	const { user, fetchUser } = useAuth();
	const { step } = useParams();
	const navigate = useNavigate();
	const [isPending, startTransition] = useTransition();
	const [activeStep, setActiveStep] = useState<number>(0);
	const [lifeData, setLifeData] = useState<AllLifeDataType | null>(null);
	const [loading, setLoading] = useState<boolean>(true);
	const [hasUnsavedChanges, setHasUnsavedChanges] = useState<boolean>(false);
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [nextStep, setNextStep] = useState<number>(0);
	const [completedSteps, setCompletedSteps] = useState<Set<number>>(
		new Set<number>()
	);
	const [highestStepVisited, setHighestStepVisited] = useState<number>(0);

	const formResetRef = useRef<(() => void) | null>(null);

	useEffect(() => {
		if (!user) {
			console.log("User not found");
			return;
		}
		const stepIndex = STEPS.findIndex(stepItem => stepItem.param === step);
		const onboardingStep = user.onboardingStep
			? user.onboardingStep - 1
			: 0;
		if (stepIndex !== -1 && stepIndex <= onboardingStep) {
			setActiveStep(stepIndex);
		} else if (user?.onboardingStep) {
			navigate(`/${STEPS[onboardingStep].param}`);
		}
	}, [step, navigate, user]);

	const fetchData = useCallback(async () => {
		try {
			const response = await apiClient.get<AllLifeDataType>(
				"/api/lifeData/allLifeData"
			);
			setLifeData(response.data);
		} catch (error) {
			console.log(`Error fetching data: ${error}`);
		} finally {
			setLoading(false);
		}
	}, []);

	useEffect(() => {
		const completed = new Set<number>();
		if (user) {
			for (let i = 0; i < user.onboardingStep - 1; i++) {
				completed.add(i);
			}
			setCompletedSteps(completed);
			setHighestStepVisited(prev =>
				Math.max(prev, (user.onboardingStep ?? 1) - 1)
			);
		}
	}, [user]);

	useEffect(() => {
		setHighestStepVisited(prev => Math.max(prev, activeStep));
	}, [activeStep]);

	const getNextStepPath = useCallback((currentStep: number) => {
		const nextStepIndex = currentStep + 1;
		return nextStepIndex < STEPS.length
			? `/${STEPS[nextStepIndex].param}`
			: null;
	}, []);

	const onStepDone = useCallback(async () => {
		try {
			setHasUnsavedChanges(false);

			startTransition(() => {
				setCompletedSteps(prev => new Set([...prev, activeStep]));
			});

			await Promise.all([fetchUser(), fetchData()]);

			const nextPath = getNextStepPath(activeStep);
			if (nextPath) {
				navigate(nextPath);
			}
		} catch (error) {
			console.error("Step completion failed:", error);
			// Revert completed steps on error
			setCompletedSteps(prev => {
				const reverted = new Set(prev);
				reverted.delete(activeStep);
				return reverted;
			});
		}
	}, [activeStep, fetchData, fetchUser, getNextStepPath, navigate]);

	useEffect(() => {
		fetchData();
	}, [fetchData]);

	useEffect(() => {
		if (!user) return;

		const pollSteps = [1, 3, 5];
		if (!pollSteps.includes(activeStep)) return;

		if (user.onboardingStep - 1 > activeStep) return;

		const intervalId = setInterval(async () => {
			try {
				const prevStep = user.onboardingStep;
				const updatedUser = await fetchUser();
				const newStep = updatedUser?.onboardingStep;
				if (newStep && newStep !== prevStep) {
					await fetchData();
					navigate(`/${STEPS[newStep - 1].param}`);
				}
			} catch (err) {
				console.error("Polling failed:", err);
			}
		}, 5000);

		return () => clearInterval(intervalId);
	}, [activeStep, fetchUser, fetchData, navigate, user]);

	const handleStepClick = (index: number) => {
		if (index > highestStepVisited) {
			return;
		}
		if (hasUnsavedChanges && index !== activeStep) {
			setIsModalOpen(true);
			setNextStep(index);
		} else {
			navigate(`/${STEPS[index].param}`);
		}
	};

	const handleConfirmNavigation = () => {
		if (formResetRef.current) {
			formResetRef.current();
		}
		setHasUnsavedChanges(false);
		navigate(`/${STEPS[nextStep].param}`);
		setIsModalOpen(false);
	};

	const handleCancelNavigation = () => {
		setIsModalOpen(false);
	};

	const renderActiveForm = () => {
		if (!user) {
			return <FullScreenLoader />;
		}

		const commonProps = {
			onFormSuccess: onStepDone,
			setHasUnsavedChanges: setHasUnsavedChanges,
			setResetForm: (resetFunc: () => void) => {
				formResetRef.current = resetFunc;
			},
		};

		const stepComponents = [
			<Profile
				isEditable={true}
				hideCancelButton={true}
				onStepDone={onStepDone}
				setHasUnsavedChanges={setHasUnsavedChanges}
			/>,
			<VideoUpload
				key="EarlyLife"
				videoType="EarlyLife"
				description={"Early Life Video"}
				setHasUnsavedChanges={setHasUnsavedChanges}
			/>,
			<EarlyLifeForm
				{...commonProps}
				lifeData={lifeData?.earlyLifeData ?? null}
			/>,
			<VideoUpload
				key="ProfessionalLife"
				videoType="ProfessionalLife"
				description={"Professional Life Video"}
				setHasUnsavedChanges={setHasUnsavedChanges}
			/>,
			<ProfessionalLifeForm
				{...commonProps}
				lifeData={lifeData?.professionalLifeData ?? null}
			/>,
			<VideoUpload
				key="CurrentLife"
				videoType="CurrentLife"
				description={"Current Life Video"}
				setHasUnsavedChanges={setHasUnsavedChanges}
			/>,
			<CurrentLifeForm
				{...commonProps}
				lifeData={lifeData?.currentLifeData ?? null}
			/>,
			<ProfileTabsWrapper />,
		];

		const activeComponent = stepComponents[activeStep] ?? null;
		if (activeStep === stepComponents.length - 1) {
			return activeComponent;
		}

		return <div style={{ padding: "2rem" }}>{activeComponent}</div>;
	};

	if (loading || isPending) {
		return <FullScreenLoader />;
	}

	return (
		<div style={{ display: "flex", height: "calc(100vh - 70px)" }}>
			<div
				style={{
					width: "270px",
					height: "100%",
					borderRight: "1px solid #ccc",
					padding: "2rem",
					paddingBottom: "2rem",
					overflowY: "auto",
				}}
			>
				<Title order={3} style={{ marginBottom: "1rem" }}>
					Onboarding
				</Title>
				<Stepper
					active={activeStep}
					orientation="vertical"
					size="sm"
					onStepClick={handleStepClick}
				>
					{STEPS.map((step, index) => (
						<Stepper.Step
							key={index}
							label={step.label}
							description={step.description}
							icon={
								completedSteps.has(index) ? (
									<IconCheck size={28} color="green" />
								) : undefined
							}
							style={{
								cursor:
									index > highestStepVisited
										? "not-allowed"
										: "pointer",
							}}
						/>
					))}
				</Stepper>
			</div>

			<div
				style={{
					flex: 1,
					paddingBottom: "2rem",
					overflowY: "auto",
				}}
			>
				{renderActiveForm()}
			</div>

			<Modal
				opened={isModalOpen}
				onClose={handleCancelNavigation}
				transitionProps={{ transition: "fade", duration: 200 }}
				centered
				withCloseButton={false}
				styles={{ body: { padding: 8 } }}
			>
				<>
					<Text fw={500} size="lg" mb="xs">
						Save your changes before leaving
					</Text>
					<Text mb="lg">
						You have unsaved changes that will be permanently lost
						if you leave this page.
					</Text>
					<Group justify="flex-end">
						<Button
							variant="default"
							onClick={handleCancelNavigation}
						>
							Stay on page
						</Button>
						<Button
							variant="danger"
							onClick={handleConfirmNavigation}
						>
							Leave without saving
						</Button>
					</Group>
				</>
			</Modal>
		</div>
	);
}

export default OnBoardingStepper;
