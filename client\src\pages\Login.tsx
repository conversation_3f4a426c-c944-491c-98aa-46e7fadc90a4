import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import {
	TextInput,
	PasswordInput,
	Paper,
	Button,
	Stack,
	Center,
	Text,
	Image,
	Container,
} from "@mantine/core";
import { IconX, IconCheck } from "@tabler/icons-react";
import { notifications } from "@mantine/notifications";
import { isAxiosError } from "axios";

export default function Login() {
	const [email, setEmail] = useState("");
	const [password, setPassword] = useState("");
	const [loading, setLoading] = useState(false);
	const [emailError, setEmailError] = useState("");
	const navigate = useNavigate();
	const { login } = useAuth();

	const validateEmail = (email: string): boolean => {
		const trimmnedEmail = email.trim();

		if (!trimmnedEmail) {
			setEmailError("Email is required");
			return false;
		}
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(trimmnedEmail)) {
			setEmailError("Please enter a valid email address");
			return false;
		}
		setEmailError("");
		return true;
	};

	const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
		e.preventDefault();

		if (!validateEmail(email)) {
			return;
		}

		if (!password) {
			notifications.show({
				title: "Validation Error",
				message: "Password is required",
				color: "red",
				icon: <IconX />,
			});
			return;
		}

		setLoading(true);
		try {
			const loggedInUser = await login(
				email.trim().toLowerCase(),
				password
			);
			notifications.show({
				title: "Welcome back",
				message: "You've successfully logged in.",
				color: "green",
				icon: <IconCheck />,
			});
			if (loggedInUser && loggedInUser.role === 3) {
				navigate("/profile");
			} else {
				navigate("/users");
			}
		} catch (error) {
			if (isAxiosError(error)) {
				notifications.show({
					title: "Login failed",
					message:
						error.response?.data?.error ||
						"User not found or invalid credentials",
					color: "red",
					icon: <IconX />,
				});
			} else {
				notifications.show({
					title: "Login failed",
					message: "An unexpected error occurred",
					color: "red",
					icon: <IconX />,
				});
			}
		} finally {
			setLoading(false);
		}
	};

	return (
		<Center mih="100vh" bg="gray.0">
			<Paper withBorder shadow="md" radius="md" p="xl" w={360}>
				<Stack gap="sm" align="center">
					<Container w={100}>
						<Center>
							<Image
								src="https://cdn.dribbble.com/userupload/24539687/file/original-e456c63ed32649e95da831e9ec4e6e0a.jpg?resize=400x0"
								alt="logo"
								height={10}
								width={10}
								radius="sm"
								fit="fill"
							/>
						</Center>
					</Container>

					<Stack gap={2} align="center">
						<Text
							size="lg"
							ff="monospace"
							fz={30}
							fw={500}
							c="dimmed"
							ta="center"
						>
							360 APP
						</Text>
						<Text size="md" fw={500} c="dimmed" ta="center"></Text>
					</Stack>
				</Stack>

				<br />

				<form onSubmit={handleSubmit}>
					<Stack gap="md" mt="md">
						<TextInput
							label="Email"
							placeholder="<EMAIL>"
							required
							value={email}
							onChange={e => {
								setEmail(e.target.value);
								if (emailError) validateEmail(e.target.value);
							}}
							error={emailError}
							disabled={loading}
						/>
						<PasswordInput
							label="Password"
							placeholder="Your password"
							required
							value={password}
							onChange={e => setPassword(e.target.value)}
							disabled={loading}
						/>

						<Text size="sm" c="dimmed" ta="center">
							<Link
								to="/forgot-password"
								style={{
									textDecoration: "none",
									color: "grey",
								}}
							>
								Forgot your password?
							</Link>
						</Text>

						<Button
							type="submit"
							loading={loading}
							fullWidth
							size="md"
						>
							{loading ? "Logging in..." : "Log in"}
						</Button>
					</Stack>
				</form>
			</Paper>
		</Center>
	);
}
