# 360 App

This is a full-stack application with a React client and a Node.js server.

## Project Structure

- `client/`: Contains the React front-end application.
- `server/`: Contains the Node.js back-end application.

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- npm
- MongoDB

### Setup

1. **Clone the repository:**

   ```bash
   <NAME_EMAIL>:CQ-Dev-Team/360.git
   cd 360-app
   ```

## Mailpit Setup (for Local Email Testing)

[Mailpit](https://github.com/axllent/mailpit) is a local SMTP server and email viewer that's useful for testing email functionality during development.

1. **Run Mailpit using Docker:**

    ```bash
    docker run -d --name mailpit -p 1025:1025 -p 8025:8025 axllent/mailpit
    ```

2. **Access the Mailpit UI:**

    Open your web browser and go to [http://localhost:8025](http://localhost:8025) to view the emails sent by the application.

3. **Configure the server:**

    Update your `server/.env` file with the following settings to route emails to Mailpit:

    ```env
    EMAIL_HOST=localhost
    EMAIL_PORT=1025
    EMAIL_USER=
    EMAIL_PASS=
    ```

4. **Set up the server:**

   - Navigate to the `server` directory:

     ```bash
     cd server
     ```

   - Install dependencies:

     ```bash
     npm install
     ```

   - Create a `.env` file by copying `.env.example` and fill in the required environment variables (e.g., database connection string, JWT secret).

     ```bash
     cp .env.example .env
     ```

5. **Set up the client:**

   - Navigate to the `client` directory:

     ```bash
     cd ../client
     ```

   - Install dependencies:

     ```bash
     npm install
     ```

   - Create a `.env` file by copying `.env.example` and fill in the required environment variables (e.g., API URL).

     ```bash
     cp .env.example .env
     ```

### Running the Application

1. **Start the server:**

   - In the `server` directory, run:

     ```bash
     npm start
     ```

   - The server will be running on the port specified in your `.env` file (e.g., <http://localhost:3000>).

2. **Start the client:**

   - In the `client` directory, run:

     ```bash
     npm run dev
     ```

   - The client will be running on <http://localhost:5173>.

## Technologies Used

### Client (Front-end)

- **React**: A JavaScript library for building user interfaces.
- **Vite**: A fast build tool and development server for modern web projects.
- **TypeScript**: A typed superset of JavaScript.
- **Mantine**: A full-featured React components library.
- **Tailwind CSS**: A utility-first CSS framework.
- **React Router**: For declarative routing in React applications.
- **Axios**: For making HTTP requests.

### Server (Back-end)

- **Node.js**: A JavaScript runtime built on Chrome's V8 JavaScript engine.
- **Express**: A minimal and flexible Node.js web application framework.
- **MongoDB**: A NoSQL document database.
- **Mongoose**: An ODM (Object Data Modeling) library for MongoDB and Node.js.
- **JWT (JSON Web Tokens)**: For user authentication.
- **Multer**: For handling `multipart/form-data`, primarily used for uploading files.
- **fluent-ffmpeg**: A wrapper for the ffmpeg command-line tool.
- **OpenAI**: For interacting with the OpenAI API.

## Available Scripts

### Client

- `npm run dev`: Starts the development server.
- `npm run build`: Builds the application for production.
- `npm run lint`: Lints the codebase.
- `npm run preview`: Serves the production build locally.

### Server

- `npm start`: Starts the server.
- `npm run nodemon`: Starts the server with nodemon for automatic restarts on file changes.
- `npm run seed:superAdmin`: Creates a super admin user.
