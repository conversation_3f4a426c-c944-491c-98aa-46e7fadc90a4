import {
	<PERSON>ack,
	Group,
	Text,
	Divider,
	ThemeIcon,
	Flex,
	Box,
	Button,
} from "@mantine/core";
import {
	IconMapPin,
	IconHome,
	IconSchool,
	IconBackpack,
	IconTags,
} from "@tabler/icons-react";
import React, { useEffect, useState } from "react";
import apiClient from "../../config/axios";
import type { EarlyLifeDataType } from "../../types";
import VideoPreviewAndUpload from "../VideoPreviewAndUpload";
import FullScreenLoader from "../FullScreenLoader";

interface EarlyLifePreviewProps {
	showEdit?: boolean;
	setEditing?: (value: boolean) => void;
	lifeData?: EarlyLifeDataType;
}

const EarlyLifePreview: React.FC<EarlyLifePreviewProps> = ({
	showEdit,
	setEditing,
	lifeData,
}) => {
	const [earlyLife, setEarlyLife] = useState<EarlyLifeDataType | null>(null);
	const fetchData = async () => {
		try {
			const response = await apiClient.get<EarlyLifeDataType>(
				"/api/lifeData/earlyLife"
			);
			setEarlyLife(response.data);
		} catch (err) {
			console.error(`Error fetching early life data: ${err}`);
		}
	};
	useEffect(() => {
		if (lifeData) {
			setEarlyLife(lifeData);
		} else {
			fetchData();
		}
	}, [lifeData]);

	if (!earlyLife) return <FullScreenLoader />;

	return (
		<>
			{showEdit && (
				<Flex justify="flex-end">
					<Button onClick={() => setEditing?.(true)} mb={8}>
						Edit
					</Button>
				</Flex>
			)}
			<VideoPreviewAndUpload
				editing={true}
				videoPreviewUrl={earlyLife.videoUrl}
				videoType="EarlyLife"
			/>

			<Stack>
				<Group gap="xs">
					<ThemeIcon variant="light" color="blue">
						<IconMapPin size={16} />
					</ThemeIcon>
					<Stack gap={-4}>
						<Text c="dimmed" size="xs">
							Birth City:
						</Text>
						<Text size="sm">{earlyLife.birthCity || "N/A"}</Text>
					</Stack>
				</Group>

				<Group gap="xs">
					<ThemeIcon variant="light" color="green">
						<IconHome size={16} />
					</ThemeIcon>
					<Stack gap={-4}>
						<Text c="dimmed" size="xs">
							Hometown City:
						</Text>
						<Text size="sm">{earlyLife.hometownCity || "N/A"}</Text>
					</Stack>
				</Group>

				<Divider label="Schools" labelPosition="center" />

				{earlyLife.schools && earlyLife.schools.length > 0 ? (
					earlyLife.schools.map((school, index) => (
						<Group key={index} gap="xs">
							<ThemeIcon variant="light" color="teal">
								<IconBackpack size={16} />
							</ThemeIcon>
							<Stack gap={-4}>
								<Text size="sm">{school.name || "N/A"}</Text>
								<Text c="dimmed" size="xs">
									{school.location || "N/A"}
								</Text>
							</Stack>
						</Group>
					))
				) : (
					<Text size="sm" c="dimmed" className="pl-1">
						No school to display.
					</Text>
				)}

				<Divider label="Universities" labelPosition="center" />

				{earlyLife.universities && earlyLife.universities.length > 0 ? (
					earlyLife.universities.map((uni, index) => (
						<Group key={index} gap="xs">
							<ThemeIcon variant="light" color="violet">
								<IconSchool size={16} />
							</ThemeIcon>
							<Stack gap={-4}>
								<Group>
									<Text size="sm">{`${uni.name || "N/A"}, ${uni.location || "N/A"}`}</Text>
								</Group>
								<Text c="dimmed" size="xs">
									{uni.course || "N/A"}
								</Text>
							</Stack>
						</Group>
					))
				) : (
					<Text size="sm" c="dimmed" className="pl-1">
						No university to display.
					</Text>
				)}

				{}

				<Divider label="Tags" labelPosition="center" />

				<Flex align="start" gap="sm">
					<ThemeIcon variant="light" color="yellow" mt={2}>
						<IconTags size={16} />
					</ThemeIcon>

					<Box style={{ flex: 1 }}>
						{earlyLife.earlyLifeTags.length > 0 ? (
							<Flex wrap="wrap" gap="xs">
								{earlyLife.earlyLifeTags.map((tag, idx) => (
									<Text
										key={idx}
										size="sm"
										className="bg-gray-100 text-sm !px-3 !py-1 rounded-full text-gray-700"
									>
										{tag}
									</Text>
								))}
							</Flex>
						) : (
							<Text size="sm" c="dimmed">
								No tags to display.
							</Text>
						)}
					</Box>
				</Flex>
			</Stack>
		</>
	);
};

export default EarlyLifePreview;
