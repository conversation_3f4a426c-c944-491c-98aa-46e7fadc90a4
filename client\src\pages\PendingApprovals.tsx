import { useCallback, useEffect, useState } from "react";
import { apiClient } from "../config/axios";
import {
	Container,
	Title,
	Divider,
	Group,
	Loader,
	Center,
	Pagination,
	Text,
	NativeSelect,
	Paper,
	Stack,
	TextInput,
} from "@mantine/core";
import UserTable from "../components/profile-approveForms/UserTable";
import { notifications } from "@mantine/notifications";
import { IconSearch, IconSortAscending, IconX } from "@tabler/icons-react";
import type { User } from "../types";
import { useAuth } from "../contexts/AuthContext";
import { useDebouncedValue } from "@mantine/hooks";
import { DEBOUNCE_TIME_IN_MS } from "../constants";

const PendingApprovals = () => {
	const [users, setUsers] = useState<User[]>([]);
	const [page, setPage] = useState(1);
	const [totalPages, setTotalPages] = useState(1);
	const [loading, setLoading] = useState(true);
	const { user } = useAuth();
	const currentUserRole: number = user?.role ?? 0;
	const [limit, setLimit] = useState(5);
	const [totalUsers, setTotalUsers] = useState(0);
	const start = (page - 1) * limit + 1;
	const end = Math.min(page * limit, totalUsers);
	const [sortBy, setSortBy] = useState("firstName");
	const [searchQuery, setSearchQuery] = useState<string>("");
	const [profileStatus, setProfileStatus] = useState<string>("all");
	const [debouncedSearch] = useDebouncedValue(
		searchQuery,
		DEBOUNCE_TIME_IN_MS
	);

	const fetchUsers = useCallback(
		async (currentPage: number) => {
			setLoading(true);
			try {
				const res = await apiClient.get(
					`/api/users/pending-approvals?page=${currentPage}&limit=${limit}&sort=${sortBy}&search=${encodeURIComponent(debouncedSearch.trim())}&profileStatus=${profileStatus}`
				);

				setUsers(res.data.data);
				setTotalPages(res.data.totalPages);
				setTotalUsers(res.data.total);
			} catch (err) {
				console.error("Error fetching users:", err);
				notifications.show({
					title: "Error",
					message: "Failed to fetch users",
					color: "red",
					icon: <IconX />,
				});
			} finally {
				setLoading(false);
			}
		},
		[limit, sortBy, debouncedSearch, profileStatus]
	);

	useEffect(() => {
		fetchUsers(page);
	}, [page, fetchUsers]);

	// const handleApproveUser = async (userId: string) => {
	//     try {
	//         await apiClient.put(`/api/users/approve/${userId}`);
	//         setUsers((prev) => prev.filter((u) => u._id !== userId));
	//         notifications.show({
	//             title: "User Approved",
	//             message: "User has been approved successfully",
	//             color: "green",
	//             icon: <IconCheck />,
	//         });
	//     } catch {
	//         notifications.show({
	//             title: "Failed",
	//             message: "Failed to approve user",
	//             color: "red",
	//             icon: <IconX />,
	//         });
	//     }
	// }

	return (
		<Container>
			<Title order={1}>Pending Approvals</Title>
			<Divider my="md" />

			<Paper withBorder p="md" mb="md">
				<Stack gap={6}>
					<Group justify="space-between" align="center">
						<Group align="center" gap="sm">
							<TextInput
								placeholder="Search by name or email"
								leftSection={<IconSearch size={16} />}
								value={searchQuery}
								w={300}
								onChange={e => {
									setSearchQuery(e.target.value);
									setPage(1);
								}}
							/>
							<NativeSelect
								leftSection={<IconSortAscending size={14} />}
								w={180}
								value={sortBy}
								onChange={e => {
									setSortBy(e.target.value);
									setPage(1);
								}}
								data={[
									{ label: "First Name", value: "firstName" },
									{ label: "Last Name", value: "secondName" },
									{ label: "Email", value: "email" },
								]}
								size="sm"
							/>

							<NativeSelect
								leftSection={<IconSortAscending size={14} />}
								w={180}
								value={profileStatus}
								onChange={e => {
									setProfileStatus(e.target.value);
								}}
								data={[
									{ label: "All", value: "all" },
									{ label: "Pending", value: "pending" },
									{
										label: "Re-approval",
										value: "re-approved",
									},
								]}
								size="sm"
							/>
						</Group>

						<NativeSelect
							w={180}
							value={String(limit)}
							onChange={e => {
								setLimit(Number(e.target.value));
								setPage(1);
							}}
							data={[
								{ label: "5 per page", value: "5" },
								{ label: "10 per page", value: "10" },
								{ label: "20 per page", value: "20" },
							]}
							size="sm"
						/>
					</Group>

					<Text c="gray.7" size="sm">
						Showing {start}-{end} of {totalUsers} users
					</Text>
				</Stack>
			</Paper>

			<div>
				{loading ? (
					<Center>
						<Loader />
					</Center>
				) : (
					<>
						<UserTable
							users={users}
							currentUserRole={currentUserRole}
						/>

						<Group justify="flex-end">
							<Pagination
								total={totalPages}
								value={page}
								onChange={setPage}
								mt="md"
								siblings={1}
								boundaries={1}
							/>
						</Group>
					</>
				)}
			</div>
		</Container>
	);
};

export default PendingApprovals;
