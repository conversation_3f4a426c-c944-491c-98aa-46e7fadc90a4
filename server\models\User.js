import mongoose from "mongoose";
import { ONBOARDING_STEP } from "../constants/index.js";

const EarlyLifeSchema = new mongoose.Schema(
	{
		birthCity: { type: String },
		hometownCity: { type: String },
		schools: [
			{
				name: { type: String },
				location: { type: String },
			},
		],
		universities: [
			{
				name: { type: String },
				course: { type: String },
				location: { type: String },
			},
		],
		earlyLifeTags: [{ type: String }],
		videoId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: "VideoUpload",
		},
	},
	{ _id: false }
);

const ProfessionalLifeSchema = new mongoose.Schema(
	{
		firstJob: {
			companyName: { type: String },
			roles: [{ type: String }],
		},
		subsequentJobs: [
			{
				companyName: { type: String },
				roles: [{ type: String }],
			},
		],
		professionalLifeTags: [{ type: String }],
		videoId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: "VideoUpload",
		},
	},
	{ _id: false }
);

const CurrentLifeSchema = new mongoose.Schema(
	{
		currentLifeSummary: { type: String, maxLength: 500 },
		currentCities: [{ type: String }],
		currentOrganizations: [
			{
				name: { type: String },
				role: { type: String },
			},
		],
		frequentTravelCities: [{ type: String }],
		currentLifeTags: [{ type: String }],
		videoId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: "VideoUpload",
		},
	},
	{ _id: false }
);

const userSchema = new mongoose.Schema(
	{
		firstName: {
			type: String,
			required: true,
		},
		middleName: {
			type: String,
		},
		secondName: {
			type: String,
			required: true,
		},
		email: {
			type: String,
			required: true,
			unique: true,
		},
		password: {
			type: String,
			required: true,
			select: false,
		},
		mobile: {
			type: String,
			required: true,
		},
		role: {
			type: Number,
			required: true,
		},
		onboardingStep: {
			type: Number,
			enum: ONBOARDING_STEP,
			default: ONBOARDING_STEP.BASIC_DETAILS,
		},
		profileStatus: {
			type: String,
			enum: ["pending", "approved", "re-approved"],
			default: "pending",
		},
		referer: {
			type: mongoose.Schema.Types.ObjectId,
			ref: "User",
		},
		curator: {
			type: mongoose.Schema.Types.ObjectId,
			ref: "User",
		},
		resetPasswordToken: {
			type: String,
		},
		resetPasswordExpires: {
			type: Date,
		},
		displayStatus: {
			type: Boolean,
			default: true,
		},
		// General Profile Details
		title: { type: String },
		image: { type: String },
		address: { type: String },
		// birthday: { type: String },
		city: { type: String },
		// gender: { type: String },
		introduction: { type: String },
		quote: { type: String },
		joy: { type: String },
		contentLinks: { type: [String] },
		currentOrganization: { type: String },
		// workplaceAddress: { type: String },

		// Skills & Socials
		otherSocialHandles: { type: [String] },
		skills: { type: [String] },
		twitter: { type: String },
		instagram: { type: String },
		linkedIn: { type: String },

		earlyLifeData: { type: EarlyLifeSchema, required: false },
		updatedEarlyLifeData: { type: EarlyLifeSchema, required: false },

		professionalLifeData: { type: ProfessionalLifeSchema, required: false },
		updatedProfessionalLifeData: {
			type: ProfessionalLifeSchema,
			required: false,
		},

		currentLifeData: { type: CurrentLifeSchema, required: false },
		updatedCurrentLifeData: { type: CurrentLifeSchema, required: false },

		isAdminPanelUser: { type: Boolean, default: false },
	},
	{ timestamps: true }
);

userSchema.index({ resetPasswordToken: 1, resetPasswordExpires: 1 });

const User = mongoose.model("User", userSchema);
export default User;
