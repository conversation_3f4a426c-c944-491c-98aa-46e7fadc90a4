import {
	Button,
	Flex,
	Group,
	Stack,
	Text,
	ThemeIcon,
	Title,
} from "@mantine/core";
import {
	IconCheck,
	IconInfoCircle,
	IconLoader2,
	IconRotateClockwise,
	IconSquare,
	IconVideo,
} from "@tabler/icons-react";
import { AnimatePresence, motion } from "framer-motion";
import { useCallback, useEffect, useRef, useState } from "react";
import { MAX_RECORDING_TIME, videoTypeLabel } from "../../constants";
import type { videoDataType } from "../../types";
import Instruction from "../video-instruction/Instruction";
import VideoInstructionsModal from "../video-instruction/VideoInstructionsModal";

type RecordingState = "idle" | "loading" | "recording" | "recorded";

type RecordVideoModalProps = {
	videoType: videoDataType;
	onVideoRecord: (blob: Blob) => void;
};

const VideoRecorder = (props: RecordVideoModalProps) => {
	const [recordingState, setRecordingState] =
		useState<RecordingState>("idle");
	const [recordingTime, setRecordingTime] = useState(0);
	const [stream, setStream] = useState<MediaStream | null>(null);
	const videoRef = useCallback(
		(node: HTMLVideoElement) => {
			if (node !== null && stream) {
				node.srcObject = stream;
			}
		},
		[stream]
	);
	const [recordedBlob, setRecordedBlob] = useState<Blob | null>(null);
	const [recordedVideoUrl, setRecordedVideoUrl] = useState<string>("");
	const mediaRecorderRef = useRef<MediaRecorder | null>(null);
	const intervalRef = useRef<NodeJS.Timeout | null>(null);
	const streamRef = useRef<MediaStream | null>(null);
	const [permissionError, setPermissionError] = useState<boolean>(false);
	const [showInstructions, setShowInstructions] = useState<boolean>(false);

	useEffect(() => {
		streamRef.current = stream;
	}, [stream]);

	useEffect(() => {
		return () => {
			if (streamRef.current) {
				streamRef.current.getTracks().forEach(track => track.stop());
			}
			if (intervalRef.current) {
				clearInterval(intervalRef.current);
			}
		};
	}, []);

	useEffect(() => {
		if (recordedBlob) {
			const url = URL.createObjectURL(recordedBlob);
			setRecordedVideoUrl(url);

			return () => {
				URL.revokeObjectURL(url);
				setRecordedVideoUrl("");
			};
		}
	}, [recordedBlob]);

	const formatTime = (timeInSeconds: number) => {
		const minutes = Math.floor(timeInSeconds / 60);
		const seconds = timeInSeconds % 60;
		return `${String(minutes).padStart(2, "0")}:${String(seconds).padStart(
			2,
			"0"
		)}`;
	};

	const stopRecording = useCallback(() => {
		if (
			mediaRecorderRef.current &&
			mediaRecorderRef.current.state === "recording"
		) {
			mediaRecorderRef.current.stop();
		}
		if (intervalRef.current) {
			clearInterval(intervalRef.current);
		}
	}, []);

	const startRecording = useCallback(async () => {
		try {
			setRecordingState("loading");

			const mediaStream = await navigator.mediaDevices.getUserMedia({
				video: true,
				audio: true,
			});

			setPermissionError(false);
			setStream(mediaStream);

			const mediaRecorder = new MediaRecorder(mediaStream);
			mediaRecorderRef.current = mediaRecorder;
			const chunks: BlobPart[] = [];
			mediaRecorder.ondataavailable = event => {
				chunks.push(event.data);
			};

			mediaRecorder.onstop = () => {
				const blob = new Blob(chunks, {
					type: "video/webm",
				});
				setRecordedBlob(blob);
				setRecordingState("recorded");
				mediaStream.getTracks().forEach(track => track.stop());
				setStream(null);
			};

			mediaRecorder.start();
			setRecordingState("recording");
			setRecordingTime(0);
			intervalRef.current = setInterval(() => {
				setRecordingTime(prev => {
					if (prev >= MAX_RECORDING_TIME - 1) {
						stopRecording();
						return prev;
					}
					return prev + 1;
				});
			}, 1000);
		} catch (error) {
			if ((error as Error).name === "NotAllowedError") {
				setPermissionError(true);
			}
			console.error("Error accessing camera:", error);
		}
	}, [stopRecording]);

	const handleSubmit = () => {
		if (recordedBlob) {
			props?.onVideoRecord(recordedBlob);
			handleClose();
		}
	};

	const handleClose = () => {
		setRecordingState("idle");
		setRecordingTime(0);
		setRecordedBlob(null);
		setPermissionError(false);
		setStream(null);
	};

	const renderIdleState = () => (
		<motion.div
			key="idle"
			initial={{ opacity: 0, scale: 0.95 }}
			animate={{ opacity: 1, scale: 1 }}
			exit={{ opacity: 0, scale: 0.95 }}
			transition={{ duration: 0.3 }}
		>
			<Stack align="center" mt={20}>
				<VideoInstructionsModal
					opened={showInstructions}
					onClose={() => setShowInstructions(false)}
					videoType={props.videoType}
				/>
				<ThemeIcon
					size="5rem"
					variant="light"
					color="blue"
					radius={"lg"}
				>
					<IconVideo size={"3rem"} />
				</ThemeIcon>
				<Title order={3}>Ready to Record</Title>
				{props.videoType && (
					<Flex align={"center"} justify={"center"} gap={5}>
						<Text c={"gray.6"}>
							Recording video for:{" "}
							<span className="font-semibold">
								{
									videoTypeLabel[
										props.videoType as keyof typeof videoTypeLabel
									]
								}
							</span>
						</Text>
						<Button
							size="sm"
							variant="white"
							styles={{ root: { padding: 0 } }}
							onClick={() => setShowInstructions(true)}
						>
							<IconInfoCircle size={"1.25rem"} />
						</Button>
					</Flex>
				)}
				{permissionError ? (
					<Text c="red.6" ta="center">
						Permission to access the camera and microphone was
						denied. Please allow access in your browser settings and
						try again.
					</Text>
				) : (
					<Text c={"gray.6"} mt={5}>
						Click the button below to start recording your video
					</Text>
				)}
				<Button
					w={"100%"}
					leftSection={<IconVideo size={20} />}
					variant="light"
					radius={"md"}
					onClick={startRecording}
				>
					Start Recording
				</Button>
			</Stack>
		</motion.div>
	);

	const renderLoadingState = () => (
		<motion.div
			key="loading"
			initial={{ opacity: 0, scale: 0.95 }}
			animate={{ opacity: 1, scale: 1 }}
			exit={{ opacity: 0, scale: 0.95 }}
			transition={{ duration: 0.2, ease: "easeOut" }}
		>
			<Stack align="center" mt={20}>
				<ThemeIcon
					size="5rem"
					variant="light"
					color="blue"
					radius={"lg"}
				>
					<IconLoader2 size={"3rem"} className="animate-spin" />
				</ThemeIcon>
				<Title order={3}>Preparing Camera...</Title>
				<Text c={"gray.6"} ta="center">
					Please wait while we set up your camera and microphone
				</Text>
			</Stack>
		</motion.div>
	);

	const renderRecordingState = () => (
		<motion.div
			key="recording"
			initial={{ opacity: 0, scale: 0.95 }}
			animate={{ opacity: 1, scale: 1 }}
			exit={{ opacity: 0, scale: 0.95 }}
			transition={{ duration: 0.3 }}
		>
			<Flex gap={50} mt={10}>
				<Stack style={{ textAlign: "center" }}>
					{
						<video
							ref={videoRef}
							autoPlay
							muted
							className="w-full max-w-md mx-auto rounded-lg mb-4"
						/>
					}

					<Group justify="center" align="center" mb={10}>
						<div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
						<span className="font-mono text-lg">
							{formatTime(recordingTime)} /{" "}
							{formatTime(MAX_RECORDING_TIME)}
						</span>
					</Group>
					<Button
						variant="danger"
						w={"100%"}
						leftSection={<IconSquare size={16} />}
						onClick={stopRecording}
					>
						Stop Recording
					</Button>
				</Stack>

				<Stack maw={"32rem"}>
					<Instruction videoType={props.videoType} />
				</Stack>
			</Flex>
		</motion.div>
	);

	const renderRecordedState = () => (
		<motion.div
			key="recorded"
			initial={{ opacity: 0, scale: 0.95 }}
			animate={{ opacity: 1, scale: 1 }}
			exit={{ opacity: 0, scale: 0.95 }}
			transition={{ duration: 0.3 }}
		>
			<div className="text-center py-4">
				{recordedVideoUrl && (
					<video
						src={recordedVideoUrl}
						controls
						className="w-full max-w-md mx-auto rounded-lg mb-4"
					/>
				)}
				<Group align="center" justify="center">
					<Button
						onClick={() => {
							setRecordingState("idle");
							setRecordedBlob(null);
							setRecordingTime(0);
							setPermissionError(false);
						}}
						variant="outline"
						className="flex-1"
						leftSection={<IconRotateClockwise size={16} />}
					>
						Record Again
					</Button>
					<Button
						onClick={handleSubmit}
						leftSection={<IconCheck size={16} />}
						className="flex-1"
					>
						Keep this video
					</Button>
				</Group>
			</div>
		</motion.div>
	);

	return (
		<AnimatePresence mode="wait">
			{recordingState === "idle" && renderIdleState()}
			{recordingState === "loading" && renderLoadingState()}
			{recordingState === "recording" && renderRecordingState()}
			{recordingState === "recorded" && renderRecordedState()}
		</AnimatePresence>
	);
};

export default VideoRecorder;
