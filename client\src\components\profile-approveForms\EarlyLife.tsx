import React, { useState } from "react";
import EarlyLifePreview from "../profile/EarlyLifePreview";
import EarlyLifeForm from "../onBoarding-forms/EarlyLifeForm";
import type { EarlyLifeDataType } from "../../types";

interface EarlyLifeProps {
	earlyLifeData: EarlyLifeDataType;
	fetchProfile: () => void;
	userId?: string;
}

const EarlyLife: React.FC<EarlyLifeProps> = ({
	earlyLifeData,
	fetchProfile,
	userId,
}) => {
	const [editing, setEditing] = useState(false);
	return (
		<>
			{editing ? (
				<>
					<EarlyLifeForm
						fetchProfile={fetchProfile}
						editing={editing}
						setEditing={setEditing}
						lifeData={earlyLifeData}
						userId={userId}
					/>
				</>
			) : (
				<EarlyLifePreview
					showEdit={true}
					setEditing={setEditing}
					lifeData={earlyLifeData}
				/>
			)}
		</>
	);
};

export default EarlyLife;
