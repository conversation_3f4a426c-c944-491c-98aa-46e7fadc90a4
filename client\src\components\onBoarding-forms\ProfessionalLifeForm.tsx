import {
	TextInput,
	Paper,
	Title,
	Button,
	Stack,
	Group,
	ActionIcon,
	Text,
	Flex,
} from "@mantine/core";
import { TagsInput } from "@mantine/core";
import React, { useEffect, useCallback, useRef } from "react";
import { IconTrash, IconPlus, IconX } from "@tabler/icons-react";
import apiClient from "../../config/axios";
import { notifications } from "@mantine/notifications";
import { isAxiosError } from "axios";
import VideoPreviewAndUpload from "../VideoPreviewAndUpload";
import type { ProfessionalLifeDataType } from "../../types";
import { useForm } from "@mantine/form";
import openCustomModal from "../modals/CustomModal";
import FullScreenLoader from "../FullScreenLoader";
import { useAuth } from "../../contexts/AuthContext";

interface ProfessionalLifeProps {
	onFormSuccess?: () => void;
	isEditable?: boolean;
	lifeData: ProfessionalLifeDataType | null;
	setHasUnsavedChanges?: (hasChanges: boolean) => void;
	setResetForm?: (resetFunc: () => void) => void;
	setEditing?: (value: boolean) => void;
	editing?: boolean;
	fetchProfile?: () => void;
	userId?: string;
}

const ProfessionalLifeForm: React.FC<ProfessionalLifeProps> = ({
	onFormSuccess,
	lifeData,
	isEditable = true,
	setHasUnsavedChanges,
	setResetForm,
	setEditing,
	editing,
	fetchProfile,
	userId,
}) => {
	const { user, fetchUser } = useAuth();
	const prevProfessionalLifeTags = useRef<number>(0);
	const form = useForm<ProfessionalLifeDataType>({
		initialValues: {
			firstJob: { companyName: "", roles: [""] },
			subsequentJobs: [],
			professionalLifeTags: [],
			videoUrl: "",
		},
		transformValues: values => ({
			...values,
			firstJob: {
				companyName: values.firstJob?.companyName?.trim() || "",
				roles: Array.isArray(values.firstJob?.roles)
					? values.firstJob.roles.map(role => role?.trim() || "")
					: [],
			},
			subsequentJobs: Array.isArray(values.subsequentJobs)
				? values.subsequentJobs.map(job => ({
						companyName: job?.companyName?.trim() || "",
						roles: Array.isArray(job?.roles)
							? job.roles.map(role => role?.trim() || "")
							: [],
					}))
				: [],
			professionalLifeTags: Array.isArray(values.professionalLifeTags)
				? values.professionalLifeTags.map(tag => tag?.trim() || "")
				: [],
		}),
	});

	useEffect(() => {
		const tags = form.values.professionalLifeTags;
		if (tags.length > 10) {
			if (prevProfessionalLifeTags.current <= 10) {
				notifications.show({
					title: "Tag Limit Reached",
					message: "You can only add upto 10 tags.",
					color: "red",
					icon: <IconX />,
				});
			}

			form.setFieldValue("professionalLifeTags", tags.slice(0, 10));
		}
	}, [form, form.values.professionalLifeTags]);

	const fetchData = useCallback(async () => {
		try {
			const response = await apiClient.get<ProfessionalLifeDataType>(
				"/api/lifeData/professionalLife"
			);
			const data = response.data;

			const fetchedData = {
				...data,
				firstJob: {
					...(data.firstJob || { companyName: "", roles: [] }),
					roles:
						(data.firstJob?.roles?.length ?? 0) > 0
							? data.firstJob.roles
							: isEditable
								? [""]
								: [],
				},
				subsequentJobs:
					(data.subsequentJobs?.length ?? 0) > 0
						? data.subsequentJobs
						: isEditable
							? [{ companyName: "", roles: [""] }]
							: [],
			};
			form.setValues(fetchedData);
			form.setInitialValues(fetchedData);
		} catch (err) {
			console.error(`Error fetching professional life data: ${err}`);
		}
	}, [isEditable]);

	useEffect(() => {
		if (lifeData) {
			const updatedLifeData = {
				...lifeData,
				firstJob: {
					...(lifeData.firstJob || { companyName: "" }),
					roles:
						(lifeData.firstJob?.roles?.length ?? 0) > 0
							? lifeData.firstJob.roles
							: isEditable
								? [""]
								: [],
				},
				subsequentJobs:
					(lifeData.subsequentJobs?.length ?? 0) > 0
						? lifeData.subsequentJobs
						: isEditable
							? [{ companyName: "", roles: [""] }]
							: [],
			};
			form.setValues(updatedLifeData);
			form.setInitialValues(updatedLifeData);
		} else {
			fetchData();
		}
	}, [lifeData, isEditable, fetchData]);

	const resetForm = useCallback(() => {
		form.reset();
	}, [form]);

	useEffect(() => {
		setResetForm?.(resetForm);
	}, [resetForm]);

	useEffect(() => {
		setHasUnsavedChanges?.(form.isDirty());
	}, [form.values]);

	const handleSubmit = async (values: ProfessionalLifeDataType) => {
		if (!form.isDirty() && user && user.onboardingStepCompleted) {
			notifications.show({
				title: "Already Up to Date",
				message: "No changes detected since last save.",
				color: "orange",
			});
			setEditing?.(false);
			return;
		}
		const professionalLife = {
			...values,
			firstJob: {
				...values.firstJob,
				roles: values.firstJob.roles.filter(r => r.trim() !== ""),
			},
			subsequentJobs: values.subsequentJobs
				.map(job => ({
					...job,
					roles: (job.roles ?? []).filter(r => r.trim() !== ""),
				}))
				.filter(
					job =>
						(job.companyName ?? "").trim() !== "" &&
						job.roles.length > 0
				),
			professionalLifeTags: values.professionalLifeTags.filter(
				tag => tag.trim() !== ""
			),
		};

		try {
			const response = await apiClient.post(
				"/api/lifeData/update",
				{
					professionalLife,
					userId,
				},
				{ withCredentials: true }
			);

			notifications.show({
				title: "Success",
				message: response.data.message,
				color: "green",
			});
			form.setInitialValues(professionalLife);
			fetchUser();
			onFormSuccess?.();
			setEditing?.(false);
			fetchProfile?.();
		} catch (err) {
			if (isAxiosError(err)) {
				notifications.show({
					title: "Error",
					message:
						err?.response?.data?.message || "Failed to save data",
					color: "red",
				});
			} else {
				notifications.show({
					title: "Error",
					message: "Failed to save data",
					color: "red",
				});
			}
		}
	};

	const addSubsequentJob = () => {
		form.insertListItem("subsequentJobs", { companyName: "", roles: [""] });
	};

	const removeSubsequentJob = (index: number) => {
		if (form.values.subsequentJobs.length <= 1 && !isEditable) return;
		form.removeListItem("subsequentJobs", index);
	};

	const handleCancel = () => {
		form.reset();
		setEditing?.(false);
	};

	if (!form.values) return <FullScreenLoader />;

	return (
		<Paper shadow="sm" p="xl" radius="md" withBorder>
			<Flex justify={"flex-end"} mb="sm">
				{editing && (
					<Button variant="outline" onClick={handleCancel}>
						Cancel
					</Button>
				)}
			</Flex>
			<VideoPreviewAndUpload
				editing={editing}
				videoPreviewUrl={form.values.videoUrl}
				setHasUnsavedChanges={setHasUnsavedChanges}
				videoType="ProfessionalLife"
			/>

			<Title order={2} size="h2" mb="xl">
				Professional Life Details
			</Title>
			<form>
				<Stack>
					<Title order={4}>First Job</Title>
					<TextInput
						label="Company Name"
						disabled={!isEditable}
						{...form.getInputProps("firstJob.companyName")}
						placeholder="eg: Google, Facebook, etc"
					/>

					{(form.values.firstJob.roles ?? []).length > 0 ? (
						<TagsInput
							label="Job Titles"
							placeholder={
								isEditable
									? "Add a title (eg: Analyst, Director, etc..) and press Enter"
									: ""
							}
							disabled={!isEditable}
							{...form.getInputProps(`firstJob.roles`)}
						/>
					) : (
						<Text size="sm" c="dimmed" className="pl-1">
							No role to display.
						</Text>
					)}

					<Title order={3}>Subsequent Jobs</Title>
					{form.values.subsequentJobs.length > 0 ? (
						form.values.subsequentJobs.map((_, i) => (
							<Flex key={i} align="center" gap="md">
								<Stack
									p="md"
									style={{
										border: "1px solid #eee",
										borderRadius: 8,
									}}
									w={"100%"}
								>
									<Group
										align="center"
										justify="space-between"
									>
										<TextInput
											label="Company Name"
											style={{ flex: 1 }}
											{...form.getInputProps(
												`subsequentJobs.${i}.companyName`
											)}
											disabled={!isEditable}
											placeholder="eg: Google, Facebook, etc"
										/>
									</Group>

									<TagsInput
										label="Job Titles"
										placeholder={
											isEditable
												? "Add a title (eg: Analyst, Director, etc..) and press Enter"
												: ""
										}
										disabled={!isEditable}
										{...form.getInputProps(
											`subsequentJobs.${i}.roles`
										)}
									/>
								</Stack>

								{isEditable && (
									<ActionIcon
										variant="subtle"
										color="red"
										onClick={() => {
											openCustomModal({
												confirmCallback: () =>
													removeSubsequentJob(i),
											});
										}}
										disabled={
											form.values.subsequentJobs
												.length === 1 && isEditable
										}
									>
										<IconTrash size={20} />
									</ActionIcon>
								)}
							</Flex>
						))
					) : (
						<Text size="sm" c="dimmed" className="pl-1">
							No subsequent job to display.
						</Text>
					)}

					{isEditable && (
						<Button
							variant="outline"
							size="sm"
							leftSection={<IconPlus size={16} />}
							onClick={addSubsequentJob}
						>
							Add Another Job
						</Button>
					)}

					<TagsInput
						label="Professional Life Tags"
						placeholder={
							isEditable ? "Add a tag and press Enter" : ""
						}
						disabled={!isEditable}
						{...form.getInputProps("professionalLifeTags")}
					/>

					{isEditable && (
						<Group justify="flex-end">
							<Button
								w={100}
								onClick={() => form.onSubmit(handleSubmit)()}
							>
								Save
							</Button>
						</Group>
					)}
				</Stack>
			</form>
		</Paper>
	);
};

export default ProfessionalLifeForm;
