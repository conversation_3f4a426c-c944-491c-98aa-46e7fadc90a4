import mongoose from "mongoose";
import User from "../models/User.js";
import { APP_CONFIG } from "../config/env.js";
import fs from "fs";
import path from "path";

const migrateOnboardingStep5 = async () => {
	try {
		const superadmin = await User.findOne({
			email: "<EMAIL>",
		});
		if (!superadmin) throw new Error("Superadmin not found");

		const result = await User.updateMany(
			{ onboardingStep: 5, profileStatus: "approved" },
			{
				$set: {
					onboardingStep: 10,
					profileStatus: "approved",
					referer: superadmin._id,
					curator: superadmin._id,
					address: "Bangalore",
					city: "Bangalore",
					introduction:
						"Living in Bangalore, working with Microsoft as a product engineer focused on cloud solutions, and mentoring remotely at Codebloom. Balances innovation, growth, and wellness in a fast-paced, tech-driven, globally connected environment.",
					quote: "fun",
					joy: "joy",
					currentOrganization: "360",
					displayStatus: true,
					instagram: "",
					twitter: "",
					linkedIn: "",
				},
			}
		);

		console.log("Migrated users:", result.modifiedCount);
	} catch (error) {
		console.error(error);
	}
};

const changeDisplayStatus = async () => {
	try {
		const result = await User.updateMany(
			{ displayStatus: { $exists: false } },
			{ $set: { displayStatus: true } }
		);
		console.log("Display status updated for:", result.modifiedCount);
	} catch (error) {
		console.error(error);
	}
};

const migrateOneUser = async () => {
	try {
		const result = await User.findOneAndUpdate(
			{ email: "<EMAIL>", onboardingStep: 1 },
			{ $set: { onboardingStep: 5 } },
			{ new: true }
		);

		if (result) {
			console.log(
				"Changed onboardingStep for",
				result.email,
				"to",
				result.onboardingStep
			);
		} else {
			console.log("User not found");
		}
	} catch (error) {
		console.error(error);
	}
};

const migrateImagePath = async () => {
	try {
		const users = await User.find();

		for (const user of users) {
			if (user.image && user.image.includes("/uploads/video/")) {
				// Convert relative DB path to absolute
				const oldPath = path.join(process.cwd(), user.image);

				// Build new DB path
				const newImagePath = user.image.replace(
					"/uploads/video/",
					"/uploads/image/"
				);

				// Build absolute new file path
				const newPath = path.join(process.cwd(), newImagePath);

				// Ensure directory exists
				const newDir = path.dirname(newPath);
				if (!fs.existsSync(newDir)) {
					fs.mkdirSync(newDir, { recursive: true });
				}

				// Move file if exists
				if (fs.existsSync(oldPath)) {
					fs.renameSync(oldPath, newPath);
					console.log(`📂 Moved file: ${oldPath} -> ${newPath}`);
				} else {
					console.warn(`⚠️ File not found: ${oldPath}`);
				}

				// Update DB
				user.image = newImagePath;
				await user.save();
				console.log(`✅ Updated DB for user ${user._id}`);
			}
		}

		console.log("🎉 Migration complete");
	} catch (error) {
		console.error("❌ Migration failed", error);
	}
};

const runMigration = async () => {
	try {
		await mongoose.connect(APP_CONFIG.MONGO_URI);

		// await migrateOnboardingStep5();
		// await changeDisplayStatus();
		// await migrateOneUser();
		await migrateImagePath();
		process.exit(0);
	} catch (error) {
		console.log(error);
		process.exit(1);
	}
};

runMigration();
