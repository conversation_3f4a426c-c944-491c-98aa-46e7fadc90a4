import {
	Stack,
	Group,
	Text,
	Divider,
	ThemeIcon,
	Flex,
	Box,
	Button,
} from "@mantine/core";
import {
	IconMapPin,
	IconBuildingSkyscraper,
	IconPlaneDeparture,
	IconInfoCircle,
	IconTags,
} from "@tabler/icons-react";
import React, { useEffect, useState } from "react";
import apiClient from "../../config/axios";
import type { CurrentLifeDataType, Organization } from "../../types";
import VideoPreviewAndUpload from "../VideoPreviewAndUpload";
import FullScreenLoader from "../FullScreenLoader";

interface CurrentLifePreviewProps {
	showEdit?: boolean;
	setEditing?: (value: boolean) => void;
	lifeData?: CurrentLifeDataType;
}

const CurrentLifePreview: React.FC<CurrentLifePreviewProps> = ({
	showEdit,
	setEditing,
	lifeData,
}) => {
	const [currentLife, setCurrentLife] = useState<CurrentLifeDataType | null>(
		null
	);

	const fetchData = async () => {
		try {
			const response = await apiClient.get<CurrentLifeDataType>(
				"/api/lifeData/currentLife"
			);
			setCurrentLife(response.data);
		} catch (err) {
			console.error("Error fetching current life data:", err);
		}
	};

	useEffect(() => {
		if (lifeData) {
			setCurrentLife(lifeData);
		} else {
			fetchData();
		}
	}, [lifeData]);

	if (!currentLife) return <FullScreenLoader />;

	return (
		<>
			{showEdit && (
				<Flex justify="flex-end">
					<Button onClick={() => setEditing?.(true)} mb={8}>
						Edit
					</Button>
				</Flex>
			)}
			<VideoPreviewAndUpload
				editing={true}
				videoPreviewUrl={currentLife.videoUrl}
				videoType="CurrentLife"
			/>

			<Stack gap="lg">
				<Flex gap="xs" align="flex-start">
					<Group>
						<ThemeIcon variant="light" color="blue">
							<IconInfoCircle size={16} />
						</ThemeIcon>
					</Group>
					<Stack gap={0} justify="flex-start">
						<Text c="dimmed" size="xs">
							Summary:
						</Text>
						<Text size="sm">
							{currentLife.currentLifeSummary || "N/A"}
						</Text>
					</Stack>
				</Flex>

				{currentLife.currentCities &&
					currentLife.currentCities.length > 0 && (
						<Stack gap={4}>
							<Group gap="xs">
								<ThemeIcon variant="light" color="teal">
									<IconMapPin size={16} />
								</ThemeIcon>
								<Text size="sm" fw={500}>
									Current Cities:
								</Text>
							</Group>

							<Group ml="xl" wrap="wrap" gap="xs">
								{currentLife.currentCities.map(
									(city: string, index: number) => (
										<Text
											size="sm"
											px="sm"
											py={4}
											key={index}
											className="rounded-full bg-gray-100 text-gray-700"
										>
											{city}
										</Text>
									)
								)}
							</Group>
						</Stack>
					)}

				<Divider label="Organizations" labelPosition="center" />

				{currentLife.currentOrganizations &&
				currentLife.currentOrganizations.length > 0 ? (
					currentLife.currentOrganizations.some(
						org => org.name || org.role
					) && (
						<Stack gap="xs">
							{currentLife.currentOrganizations.map(
								(org: Organization, idx: number) =>
									(org.name || org.role) && (
										<Group key={idx} gap="xs">
											<ThemeIcon
												variant="light"
												color="teal"
											>
												<IconBuildingSkyscraper
													size={16}
												/>
											</ThemeIcon>
											<Stack gap={-4}>
												<Text c="dimmed" size="xs">
													Org {idx + 1}: {org.name}
												</Text>
												<Text size="sm">
													{org.role || "N/A"}
												</Text>
											</Stack>
										</Group>
									)
							)}
						</Stack>
					)
				) : (
					<Text c="dimmed" size="sm">
						No current organizations to display.
					</Text>
				)}

				<Divider
					label="Frequent Travel Cities"
					labelPosition="center"
				/>

				<Flex align="start" gap="sm">
					<ThemeIcon variant="light" color="green" mt={2}>
						<IconPlaneDeparture size={16} />
					</ThemeIcon>

					<Box style={{ flex: 1 }}>
						{currentLife.frequentTravelCities &&
						currentLife.frequentTravelCities.length > 0 ? (
							<Flex wrap="wrap" gap="xs">
								{currentLife.frequentTravelCities.map(
									(city: string, idx: number) => (
										<Text
											key={idx}
											size="sm"
											className="bg-gray-100 text-sm !px-3 !py-1 rounded-full text-gray-700"
										>
											{city}
										</Text>
									)
								)}
							</Flex>
						) : (
							<Text size="sm" c="dimmed">
								No tags to display.
							</Text>
						)}
					</Box>
				</Flex>

				<Divider label="Tags" labelPosition="center" />

				<Flex align="start" gap="sm">
					<ThemeIcon variant="light" color="yellow" mt={2}>
						<IconTags size={16} />
					</ThemeIcon>

					<Box style={{ flex: 1 }}>
						{currentLife.currentLifeTags &&
						currentLife.currentLifeTags.length > 0 ? (
							<Flex wrap="wrap" gap="xs">
								{currentLife.currentLifeTags.map(
									(tag: string, idx: number) => (
										<Text
											key={idx}
											size="sm"
											className="bg-gray-100 text-sm !px-3 !py-1 rounded-full text-gray-700"
										>
											{tag}
										</Text>
									)
								)}
							</Flex>
						) : (
							<Text size="sm" c="dimmed">
								No tags to display.
							</Text>
						)}
					</Box>
				</Flex>
			</Stack>
		</>
	);
};

export default CurrentLifePreview;
