import { useNavigate, useParams } from "react-router-dom";
import type { previewApprovedResponseDataType } from "../types";
import { useEffect, useState } from "react";
import { AxiosError } from "axios";
import apiClient from "../config/axios";
import { notifications } from "@mantine/notifications";
import {
	IconBriefcase,
	IconClock,
	IconHome,
	IconUser,
	IconX,
} from "@tabler/icons-react";
import EarlyLifePreview from "../components/profile/EarlyLifePreview";
import ProfessionalLifePreview from "../components/profile/ProfessionalLifePreview";
import CurrentLifePreview from "../components/profile/CurrentLifePreview";
import { Tabs } from "@mantine/core";
import UserProfileCard from "../components/UserProfileCard";
import FullScreenLoader from "../components/FullScreenLoader";

const SearchedUser = () => {
	const navigate = useNavigate();
	const { userId } = useParams();

	const [userData, setUserData] =
		useState<previewApprovedResponseDataType | null>(null);
	const [loading, setLoading] = useState<boolean>(false);

	const fetchData = async () => {
		try {
			setLoading(true);
			const res = await apiClient.get<previewApprovedResponseDataType>(
				`/api/users/review-user/${userId}`
			);
			setUserData(res.data);
		} catch (err) {
			if (err instanceof AxiosError) {
				notifications.show({
					title: "Error",
					message:
						err.response?.data?.message ??
						err.message ??
						"Failed to fetch Profile Data",
					color: "red",
					icon: <IconX />,
				});
			} else {
				notifications.show({
					title: "Error",
					message: "Failed to fetch Profile Data",
					color: "red",
					icon: <IconX />,
				});
			}
			console.error(err);
			navigate("/");
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchData();
	}, [userId]);

	if (loading || !userData) {
		return <FullScreenLoader />;
	}

	return (
		<div style={{ height: "calc(100vh-120px)" }}>
			<Tabs defaultValue="profile-info">
				<Tabs.List
					style={{
						position: "sticky",
						top: 0,
						zIndex: 1,
						backgroundColor: "white",
					}}
				>
					<Tabs.Tab
						value="profile-info"
						leftSection={<IconUser size={16} />}
					>
						Profile Info
					</Tabs.Tab>
					<Tabs.Tab
						value="early-life"
						leftSection={<IconClock size={16} />}
					>
						Early Life
					</Tabs.Tab>
					<Tabs.Tab
						value="professional-life"
						leftSection={<IconBriefcase size={16} />}
					>
						Professional Life
					</Tabs.Tab>
					<Tabs.Tab
						value="current-life"
						leftSection={<IconHome size={16} />}
					>
						Current Life
					</Tabs.Tab>
				</Tabs.List>

				<Tabs.Panel value="profile-info" pt="xs">
					<UserProfileCard
						quote={userData.basicDetails.quote}
						firstName={userData.basicDetails.firstName}
						middleName={userData.basicDetails.middleName}
						lastName={userData.basicDetails.secondName}
						email={userData.basicDetails.email}
						role={userData.basicDetails.role}
						image={userData.basicDetails.image}
						address={userData.basicDetails.address}
						city={userData.basicDetails.city}
						introduction={userData.basicDetails.introduction}
						joy={userData.basicDetails.joy}
						contentLinks={userData.basicDetails.contentLinks}
						twitter={userData.basicDetails.twitter}
						instagram={userData.basicDetails.instagram}
						linkedIn={userData.basicDetails.linkedIn}
						otherSocialHandles={
							userData.basicDetails.otherSocialHandles
						}
						currentOrganization={
							userData.basicDetails.currentOrganization
						}
					/>
				</Tabs.Panel>
				<Tabs.Panel value="early-life" pt="xs">
					<EarlyLifePreview lifeData={userData.earlyLifeData} />
				</Tabs.Panel>
				<Tabs.Panel value="professional-life" pt="xs">
					<ProfessionalLifePreview
						lifeData={userData.professionalLifeData}
					/>
				</Tabs.Panel>
				<Tabs.Panel value="current-life" pt="xs">
					<CurrentLifePreview lifeData={userData.currentLifeData} />
				</Tabs.Panel>
			</Tabs>
		</div>
	);
};

export default SearchedUser;
