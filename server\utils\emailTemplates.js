export const forgotPasswordTemplate = resetLink => `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 30px; background-color: #f9f9f9; color: #333;">
        <div style="background-color: #ffffff; padding: 40px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);">
        <h1 style="color: #2c3e50; font-size: 24px; margin-bottom: 20px;">Reset Your Password</h1>
        <p style="font-size: 16px; line-height: 1.5;">
            Hi there,
        </p>
        <p style="font-size: 16px; line-height: 1.5;">
            We received a request to reset your password. To proceed, click the button below:
        </p>
        <div style="text-align: center; margin: 30px 0;">
            <a href="${resetLink}" style="padding: 12px 24px; background-color: #007bff; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: bold;">
            Reset Password
            </a>
        </div>
        <p style="font-size: 14px; color: #777;">
            If you didn’t request a password reset, you can safely ignore this email. This link will expire in 30 minutes.
        </p>
        <p style="font-size: 14px; margin-top: 30px;">
            Best regards,<br/>
            <strong>The 360 App Team</strong>
        </p>
        </div>
        <p style="text-align: center; font-size: 12px; color: #aaa; margin-top: 20px;">
        © ${new Date().getFullYear()} 360 App. All rights reserved.
        </p>
    </div>
`;

export const newUserWelcomeTemplate = (email, password, WebsiteURL) => `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 30px; background-color: #f9f9f9; color: #333;">
        <div style="background-color: #ffffff; padding: 40px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);">
        <h1 style="color: #2c3e50; font-size: 24px; margin-bottom: 20px;">Welcome to 360 App!</h1>
        <p style="font-size: 16px; line-height: 1.5;">
            Hello,
        </p>
        <p style="font-size: 16px; line-height: 1.5;">
            Your account has been created successfully. Below are your login credentials:
        </p>
        <div style="background-color: #f1f1f1; padding: 16px; border-radius: 6px; margin: 20px 0; font-size: 15px;">
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Password:</strong> ${password}</p>
            <p><strong>Website URL:</strong> ${WebsiteURL}</p>
        </div>
        <p style="font-size: 14px; color: #777;">
            Please log in using these details and remember to update your password after your first login for security.
        </p>
        <p style="font-size: 14px; margin-top: 30px;">
            We're excited to have you on board. If you have any questions or need help, feel free to reach out.
        </p>
        <p style="font-size: 14px; margin-top: 30px;">
            Best regards,<br/>
            <strong>The 360 App Team</strong>
        </p>
        </div>
        <p style="text-align: center; font-size: 12px; color: #aaa; margin-top: 20px;">
        © ${new Date().getFullYear()} 360 App. All rights reserved.
        </p>
    </div>
`;

export const allVideosProcessedTemplate = nextStepLink => `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 30px; background-color: #f9f9f9; color: #333;">
        <div style="background-color: #ffffff; padding: 40px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);">
        <h1 style="color: #2c3e50; font-size: 24px; margin-bottom: 20px;">All Videos Processed!</h1>
        <p style="font-size: 16px; line-height: 1.5;">
            Hi there,
        </p>
        <p style="font-size: 16px; line-height: 1.5;">
            We're happy to let you know that all your life videos have been successfully processed. You can now move on to the next step of your onboarding journey.
        </p>
        <div style="text-align: center; margin: 30px 0;">
            <a href="${nextStepLink}" style="padding: 12px 24px; background-color: #007bff; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: bold;">
            Continue Onboarding
            </a>
        </div>
        <p style="font-size: 14px; color: #777;">
            If you have any questions or need assistance, please don't hesitate to contact our support team.
        </p>
        <p style="font-size: 14px; margin-top: 30px;">
            Best regards,<br/>
            <strong>The 360 App Team</strong>
        </p>
        </div>
        <p style="text-align: center; font-size: 12px; color: #aaa; margin-top: 20px;">
        © ${new Date().getFullYear()} 360 App. All rights reserved.
        </p>
    </div>
`;

export const specificVideoProcessedTemplate = (videoType, nextStepLink) => `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 30px; background-color: #f9f9f9; color: #333;">
        <div style="background-color: #ffffff; padding: 40px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);">
        <h1 style="color: #2c3e50; font-size: 24px; margin-bottom: 20px;">Your ${videoType} Video is Processed!</h1>
        <p style="font-size: 16px; line-height: 1.5;">
            Hi there,
        </p>
        <p style="font-size: 16px; line-height: 1.5;">
            We're happy to let you know that your ${videoType} life video has been successfully processed. You can now continue your onboarding journey.
        </p>
        <div style="text-align: center; margin: 30px 0;">
            <a href="${nextStepLink}" style="padding: 12px 24px; background-color: #007bff; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: bold;">
            Continue Onboarding
            </a>
        </div>
        <p style="font-size: 14px; color: #777;">
            If you have any questions or need assistance, please don't hesitate to contact our support team.
        </p>
        <p style="font-size: 14px; margin-top: 30px;">
            Best regards,<br/>
            <strong>The 360 App Team</strong>
        </p>
        </div>
        <p style="text-align: center; font-size: 12px; color: #aaa; margin-top: 20px;">
        © ${new Date().getFullYear()} 360 App. All rights reserved.
        </p>
    </div>
`;

export const profileStatusUpdateTemplate = (
	userName,
	newStatus,
	profileLink
) => `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 30px; background-color: #f9f9f9; color: #333;">
        <div style="background-color: #ffffff; padding: 40px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);">
            <h1 style="color: #2c3e50; font-size: 24px; margin-bottom: 20px;">Your Profile Status Has Changed</h1>
            
            <p style="font-size: 16px; line-height: 1.5;">
                Hi ${userName || "there"},
            </p>
            
            <p style="font-size: 16px; line-height: 1.5;">
                We wanted to let you know that your profile status has been updated.
            </p>
            
            <p style="font-size: 16px; line-height: 1.5;">
                Your new status is: <strong style="color: #007bff; text-transform: capitalize;">${newStatus}</strong>.
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="${profileLink}" style="padding: 12px 24px; background-color: #007bff; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: bold;">
                    Check Your Profile
                </a>
            </div>
            
            <p style="font-size: 14px; color: #777;">
                Have questions or need a hand? Our support team is here for you anytime.
            </p>
            
            <p style="font-size: 14px; margin-top: 30px;">
                Warm regards,<br/>
                <strong>The 360 App Team</strong>
            </p>
        </div>
        
        <p style="text-align: center; font-size: 12px; color: #aaa; margin-top: 20px;">
            © ${new Date().getFullYear()} 360 App. All rights reserved.
        </p>
    </div>
`;

export const videoProcessingFailedTemplate = (videoType, retryLink) => `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 30px; background-color: #f9f9f9; color: #333;">
        <div style="background-color: #ffffff; padding: 40px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);">
            <h1 style="color: #e74c3c; font-size: 24px; margin-bottom: 20px;">We Couldn’t Process Your Video This Time</h1>
            
            <p style="font-size: 16px; line-height: 1.5;">
                Hi there,
            </p>
            
            <p style="font-size: 16px; line-height: 1.5;">
                Unfortunately, we weren’t able to finish processing your <strong>${videoType} life</strong> video this time.
            </p>
            
            <p style="font-size: 16px; line-height: 1.5;">
                Don’t worry—you can give it another try by re-uploading and processing the video again.
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="${retryLink}" style="padding: 12px 24px; background-color: #007bff; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: bold;">
                    Try Again
                </a>
            </div>
            
            <p style="font-size: 14px; color: #777;">
                Still running into problems? Our support team is here to help—just reach out and we’ll get you sorted.
            </p>
            
            <p style="font-size: 14px; margin-top: 30px;">
                Best wishes,<br/>
                <strong>The 360 App Team</strong>
            </p>
        </div>
        
        <p style="text-align: center; font-size: 12px; color: #aaa; margin-top: 20px;">
            © ${new Date().getFullYear()} 360 App. All rights reserved.
        </p>
    </div>
`;

export const userDeletionNotificationTemplate = userName => `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 30px; background-color: #f9f9f9; color: #333;">
        <div style="background-color: #ffffff; padding: 40px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);">
        <h1 style="color: #2c3e50; font-size: 24px; margin-bottom: 20px;">Account Deletion Notification</h1>
        <p style="font-size: 16px; line-height: 1.5;">
            Hi ${userName || "there"},
        </p>
        <p style="font-size: 16px; line-height: 1.5;">
            This email is to confirm that your account with 360 App has been successfully deleted.
        </p>
        <p style="font-size: 16px; line-height: 1.5;">
            We're sorry to see you go. If you have any feedback, we'd love to hear it.
        </p>
        <p style="font-size: 14px; margin-top: 30px;">
            Best regards,<br/>
            <strong>The 360 App Team</strong>
        </p>
        </div>
        <p style="text-align: center; font-size: 12px; color: #aaa; margin-top: 20px;">
        © ${new Date().getFullYear()} 360 App. All rights reserved.
        </p>
    </div>
`;

export const maxRetryLimitTemplate = (videoType, userEmail) => `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 30px; background-color: #f9f9f9; color: #333;">
        <div style="background-color: #ffffff; padding: 40px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);">
        <h1 style="color: #e74c3c; font-size: 24px; margin-bottom: 20px;">Maximum Retry Limit Reached</h1>
        <p style="font-size: 16px; line-height: 1.5;">
            Hi there,
        </p>
        <p style="font-size: 16px; line-height: 1.5;">
            This is to notify you that the user with the email address <strong>${userEmail}</strong> has reached the maximum number of retries for a ${videoType} video.
        </p>
        <p style="font-size: 16px; line-height: 1.5;">
            No further attempts will be allowed for this user until the issue is manually resolved. Please review the user's activity and take the necessary action.
        </p>
        <p style="font-size: 14px; margin-top: 30px;">
            Best regards,<br/>
            <strong>The 360 App Team</strong>
        </p>
        </div>
        <p style="text-align: center; font-size: 12px; color: #aaa; margin-top: 20px;">
        © ${new Date().getFullYear()} 360 App. All rights reserved.
        </p>
    </div>
`;
