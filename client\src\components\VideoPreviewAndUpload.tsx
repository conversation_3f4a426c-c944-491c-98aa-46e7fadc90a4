import {
	Group,
	Box,
	Title,
	Stack,
	Text,
	<PERSON><PERSON>,
	Card,
	Progress,
	ProgressLabel,
} from "@mantine/core";
import { useEffect, useRef, useState } from "react";
import { notifications } from "@mantine/notifications";
import axios, { isAxiosError } from "axios";
import apiClient from "../config/axios";
import type { videoDataType } from "../types";
import { IconX } from "@tabler/icons-react";
import { VIDEO_UPLOAD_MAX_SIZE_IN_MB, videoTypeLabel } from "../constants";

interface VideoPreviewAndUploadProps {
	videoPreviewUrl: string | null;
	videoType: videoDataType;
	setHasUnsavedChanges?: (hasUnsavedChanges: boolean) => void;
	editing?: boolean;
}

const VideoPreviewAndUpload = ({
	videoPreviewUrl,
	videoType,
	setHasUnsavedChanges,
	// editing,
}: VideoPreviewAndUploadProps) => {
	const [selectedFile, setSelectedFile] = useState<File | null>(null);
	const fileRef = useRef<HTMLInputElement>(null);
	const [videoUrl, setVideoUrl] = useState<string>("");
	const [isUploading, setIsUploading] = useState<boolean>(false);
	const [progress, setProgress] = useState<number>(0);

	const handleFileSelect = async (file: File) => {
		if (!file) return;

		if (!file.type.startsWith("video/")) {
			notifications.show({
				title: "Invalid File Type",
				message: "Please select a video file",
				color: "red",
				icon: <IconX />,
			});
			return;
		}
		const maxSizeInBytes = VIDEO_UPLOAD_MAX_SIZE_IN_MB * 1024 * 1024;
		if (file.size > maxSizeInBytes) {
			notifications.show({
				title: "File too large",
				message: `Please select a video smaller than ${VIDEO_UPLOAD_MAX_SIZE_IN_MB}MB.`,
				color: "red",
				icon: <IconX />,
			});
			return;
		}

		setSelectedFile(file);
		setHasUnsavedChanges?.(true);
		const url = URL.createObjectURL(file);
		setVideoUrl(url);
	};

	const handleBack = () => {
		console.log("Hello");
		setSelectedFile(null);
		setVideoUrl("");
		if (fileRef.current) {
			fileRef.current.value = "";
		}
		setHasUnsavedChanges?.(false);
	};

	const handleUpload = async () => {
		if (!selectedFile) {
			notifications.show({
				title: "Video not selected",
				message: "Please select a video file to upload",
				color: "red",
			});
			return;
		}

		try {
			setIsUploading(true);
			const { data } = await apiClient.post("/api/videos/upload", {
				videoType,
				contentType: selectedFile.type,
				fileName: selectedFile.name,
			});

			const { signedUrl, videoId } = data;
			await axios.put(signedUrl, selectedFile, {
				headers: {
					"Content-Type": selectedFile.type,
				},
				onUploadProgress: progressEvent => {
					if (progressEvent.total) {
						const percentCompleted = Math.round(
							(progressEvent.loaded * 100) / progressEvent.total
						);
						setProgress(percentCompleted);
					}
				},
			});

			await apiClient.post("/api/videos/upload/success", {
				videoType,
				videoId,
			});

			setIsUploading(false);
			setSelectedFile(null);
			setHasUnsavedChanges?.(false);
			setVideoUrl("");
			setProgress(0);
			notifications.show({
				title: "Upload Successful",
				message:
					data.message ??
					"Once the video data is extracted, we will notify you by email.",
				color: "green",
			});
		} catch (error) {
			setIsUploading(false);
			if (isAxiosError(error)) {
				notifications.show({
					title: "Error",
					message:
						error.response?.data?.message ||
						"Failed to upload video.",
					color: "red",
				});
			} else {
				notifications.show({
					title: "Error",
					message: "Failed to upload video.",
					color: "red",
				});
			}
		}
	};

	useEffect(() => {
		const handleBeforeUnload = (event: BeforeUnloadEvent) => {
			if (isUploading) {
				event.preventDefault();
			}
		};

		window.addEventListener("beforeunload", handleBeforeUnload);

		return () => {
			window.removeEventListener("beforeunload", handleBeforeUnload);
		};
	}, [isUploading]);

	return (
		<Group justify="space-between" align="center" mb={32} wrap="nowrap">
			<Card
				shadow="sm"
				radius="lg"
				withBorder
				h={"28rem"}
				w={"48%"}
				p={"lg"}
			>
				<Stack h="100%" justify="center">
					<Stack gap={1}>
						<Title order={2}>
							{videoTypeLabel[videoType]} Video
						</Title>
						<Text c="gray">Watch the uploaded video.</Text>
					</Stack>
					<Box
						style={{
							backgroundColor: "black",
							borderRadius: "var(--mantine-radius-md)",
							overflow: "hidden",
							height: "70%",
							width: "100%",
						}}
					>
						<video
							controls
							src={videoPreviewUrl ? videoPreviewUrl : ""}
							style={{
								width: "100%",
								height: "100%",
								objectFit: "contain",
							}}
						/>
					</Box>
					<Group justify="flex-end">
						<input
							ref={fileRef}
							type="file"
							accept="video/*"
							onChange={e =>
								e.target.files &&
								handleFileSelect(e.target.files[0])
							}
							style={{ display: "none" }}
						/>
						{/* {!editing && (
							<Button
								onClick={e => {
									e.stopPropagation();
									fileRef.current?.click();
								}}
							>
								Upload Another Video
							</Button>
						)} */}
					</Group>
				</Stack>
			</Card>

			{selectedFile && (
				<Card
					shadow="sm"
					radius="lg"
					withBorder
					h={"28rem"}
					w={"48%"}
					p={"lg"}
				>
					<Stack h="100%" justify="flex-start">
						<Stack gap={1}>
							<Title order={2}>Uploaded Video Preview</Title>
							<Text c="gray">
								Here's a preview of the video you've uploaded.
							</Text>
						</Stack>
						<Box
							style={{
								backgroundColor: "black",
								borderRadius: "var(--mantine-radius-md)",
								overflow: "hidden",
								height: "70%",
								width: "100%",
							}}
						>
							<video
								controls
								src={videoUrl}
								style={{
									width: "100%",
									height: "100%",
									objectFit: "contain",
								}}
							>
								Your browser does not support the video tag.
							</video>
						</Box>

						{isUploading && (
							<Stack>
								<Progress.Root size={"xl"} radius={"lg"}>
									<Progress.Section
										value={progress}
										striped
										animated
									>
										<ProgressLabel>
											{progress}%
										</ProgressLabel>
									</Progress.Section>
								</Progress.Root>
							</Stack>
						)}
						<Group justify="flex-end">
							<Button
								variant="outline"
								onClick={handleBack}
								hidden={isUploading || !selectedFile}
							>
								Cancel
							</Button>
							<Button
								onClick={handleUpload}
								hidden={isUploading || !selectedFile}
							>
								Upload
							</Button>
						</Group>
					</Stack>
				</Card>
			)}
		</Group>
	);
};

export default VideoPreviewAndUpload;
