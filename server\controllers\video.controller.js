import mongoose from "mongoose";
import { getSignedUrlForUpload } from "../services/awsSpace.js";
import VideoUpload from "../models/VideoUpload.js";
import User from "../models/User.js";
import { addVideoToQueue } from "../services/videoQueue.js";
import { USER_FIELD_MAP, videoTypeStrToNum } from "../constants/index.js";

export const uploadVideo = async (req, res) => {
	try {
		const { videoType, contentType, fileName } = req.body;
		if (!videoType || !Object.keys(videoTypeStrToNum).includes(videoType)) {
			return res.status(400).json({
				message: "Invalid video type.",
			});
		}

		const videoId = new mongoose.Types.ObjectId();
		const signedUrl = await getSignedUrlForUpload(
			`SM360/onboarding/${req.user._id}/${videoId}/${fileName}`,
			contentType
		);

		res.status(200).json({
			message: "Signed URL generated successfully.",
			signedUrl,
			videoId,
		});
	} catch (error) {
		console.log(error);
		res.status(500).json({
			message: "Error generating signed URL.",
			error,
		});
	}
};

export const confirmUpload = async (req, res) => {
	try {
		const { videoId, videoType } = req.body;

		const video = await VideoUpload.create({
			_id: videoId,
			createdBy: req.user._id,
			videoType: videoType,
		});

		// Update user with the latest video of the given type
		const user = await User.findById(req.user._id);
		const stepKey = "onboardingLifeData";
		const targetField = USER_FIELD_MAP[videoType]?.[stepKey];

		if (user) {
			if (!user[targetField]) {
				user[targetField] = {};
			}
			user[targetField].videoId = videoId;
			await user.save();
		}

		addVideoToQueue({
			video,
			userId: req.user._id,
		});

		res.status(200).json({
			message: "Video upload confirmed and is being processed.",
			video,
		});
	} catch (error) {
		console.log(error);
		res.status(500).json({
			message: "Error confirming video upload.",
			error,
		});
	}
};

export const getVideoStatus = async (req, res) => {
	try {
		const user = await User.findById(req.user._id);
		if (!user) {
			return res.status(404).json({ message: "User not found." });
		}

		const earlyLifeData = user.earlyLifeData;
		const professionalLifeData = user.professionalLifeData;
		const currentLifeData = user.currentLifeData;

		let earlyVideo = null;
		let professionalVideo = null;
		let currentVideo = null;
		let earlyVideoId = null;
		let professionalVideoId = null;
		let currentVideoId = null;

		if (earlyLifeData && earlyLifeData.videoId) {
			earlyVideo = await VideoUpload.findById(
				user.earlyLifeData?.videoId
			);
			if (earlyVideo) {
				earlyVideoId =
					earlyVideo.transcriptionStatus !== "failed"
						? user.earlyLifeData?.videoId
						: null;
			}
		}

		if (professionalLifeData && professionalLifeData.videoId) {
			professionalVideo = await VideoUpload.findById(
				user.professionalLifeData?.videoId
			);
			if (professionalVideo) {
				professionalVideoId =
					professionalVideo.transcriptionStatus !== "failed"
						? user.professionalLifeData?.videoId
						: null;
			}
		}

		if (currentLifeData && currentLifeData.videoId) {
			currentVideo = await VideoUpload.findById(
				user.currentLifeData?.videoId
			);
			if (currentVideo) {
				currentVideoId =
					currentVideo.transcriptionStatus !== "failed"
						? user.currentLifeData?.videoId
						: null;
			}
		}

		res.status(200).json({
			earlyLifeVideo: earlyVideoId,
			professionalLifeVideo: professionalVideoId,
			currentLifeVideo: currentVideoId,
		});
	} catch (error) {
		res.status(500).json({
			message: "Error fetching video status.",
			error,
		});
	}
};
