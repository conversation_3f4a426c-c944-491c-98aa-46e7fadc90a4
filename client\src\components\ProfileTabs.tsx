import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Badge } from "@mantine/core";
import Profile from "../pages/Profile";
import EarlyLifePreview from "./profile/EarlyLifePreview";
import ProfessionalLifePreview from "./profile/ProfessionalLifePreview";
import CurrentLifePreview from "./profile/CurrentLifePreview";
import { useMemo, useState } from "react";
import EarlyLifeForm from "./onBoarding-forms/EarlyLifeForm";
import { Edit } from "lucide-react";
import ProfessionalLifeForm from "./onBoarding-forms/ProfessionalLifeForm";
import CurrentLifeForm from "./onBoarding-forms/CurrentLifeForm";
import { useAuth } from "../contexts/AuthContext";
import FullScreenLoader from "./FullScreenLoader";
import { profileStatusColorMap } from "../constants";

type ProfileTabsProps = {
	stickyTop?: number | string;
	noEditInProfile?: boolean;
};

const ProfileTabs = ({
	stickyTop = "70px",
	noEditInProfile,
}: ProfileTabsProps) => {
	const [editingTab, setEditingTab] = useState<string | null>(null);
	const { user } = useAuth();

	const handleSetEditing = (tab: string | null) => {
		setEditingTab(tab);
	};

	const isReApproved: boolean = useMemo(() => {
		if (user?.profileStatus === "re-approved") {
			return true;
		} else {
			return false;
		}
	}, [user?.profileStatus]);

	const canViewLifeTabs = useMemo(() => {
		if (!user) return false;
		if (user.onboardingStepCompleted) {
			return true;
		}
		if (user?.isAdminPanelUser) {
			return false;
		}
		return true;
	}, [user]);

	if (!user) return <FullScreenLoader />;

	return (
		<div style={{ minHeight: "calc(100vh - 250px)" }}>
			{isReApproved && (
				<Group justify="flex-end" mt={20} mb={10}>
					<Badge
						color={profileStatusColorMap["re-approved"]}
						size="md"
						radius="sm"
					>
						Pending Re-approval
					</Badge>
				</Group>
			)}
			<Tabs defaultValue="basic-details">
				<Tabs.List
					style={{
						position: "sticky",
						top: stickyTop,
						zIndex: 1,
						height: "40px",
						backgroundColor: "white",
					}}
				>
					<Tabs.Tab value="basic-details">Basic Details</Tabs.Tab>

					{canViewLifeTabs && (
						<>
							<Tabs.Tab value="early-life">
								Early Life Details
							</Tabs.Tab>
							<Tabs.Tab value="professional-life">
								Professional Life Details
							</Tabs.Tab>
							<Tabs.Tab value="current-life">
								Current Life Details
							</Tabs.Tab>
						</>
					)}
				</Tabs.List>

				<Tabs.Panel value="basic-details" style={{ padding: "2rem" }}>
					<Profile noEditInProfile={noEditInProfile} />
				</Tabs.Panel>

				{canViewLifeTabs && (
					<>
						<Tabs.Panel
							value="early-life"
							style={{ padding: "2rem" }}
						>
							{editingTab !== "early-life" ? (
								<>
									<Group justify="flex-end">
										{!noEditInProfile && (
											<Button
												onClick={() =>
													handleSetEditing(
														"early-life"
													)
												}
												m="lg"
												leftSection={<Edit size={16} />}
											>
												Edit
											</Button>
										)}
									</Group>
									<EarlyLifePreview />
								</>
							) : (
								<EarlyLifeForm
									lifeData={null}
									editing={editingTab === "early-life"}
									setEditing={isEditing =>
										handleSetEditing(
											isEditing ? "early-life" : null
										)
									}
									isEditable={editingTab === "early-life"}
									onFormSuccess={() => handleSetEditing(null)}
								/>
							)}
						</Tabs.Panel>

						<Tabs.Panel
							value="professional-life"
							style={{ padding: "2rem" }}
						>
							{editingTab !== "professional-life" ? (
								<>
									<Group justify="flex-end">
										{!noEditInProfile && (
											<Button
												m="lg"
												onClick={() =>
													handleSetEditing(
														"professional-life"
													)
												}
												leftSection={<Edit size={16} />}
											>
												Edit
											</Button>
										)}
									</Group>
									<ProfessionalLifePreview />
								</>
							) : (
								<ProfessionalLifeForm
									lifeData={null}
									editing={editingTab === "professional-life"}
									setEditing={isEditing =>
										handleSetEditing(
											isEditing
												? "professional-life"
												: null
										)
									}
									isEditable={
										editingTab === "professional-life"
									}
									onFormSuccess={() => handleSetEditing(null)}
								/>
							)}
						</Tabs.Panel>

						<Tabs.Panel
							value="current-life"
							style={{ padding: "2rem" }}
						>
							{editingTab !== "current-life" ? (
								<>
									<Group justify="flex-end">
										{!noEditInProfile && (
											<Button
												m="lg"
												onClick={() =>
													handleSetEditing(
														"current-life"
													)
												}
												leftSection={<Edit size={16} />}
											>
												Edit
											</Button>
										)}
									</Group>
									<CurrentLifePreview />
								</>
							) : (
								<CurrentLifeForm
									lifeData={null}
									editing={editingTab === "current-life"}
									setEditing={isEditing =>
										handleSetEditing(
											isEditing ? "current-life" : null
										)
									}
									isEditable={editingTab === "current-life"}
									onFormSuccess={() => handleSetEditing(null)}
								/>
							)}
						</Tabs.Panel>
					</>
				)}
			</Tabs>
		</div>
	);
};

export default ProfileTabs;
