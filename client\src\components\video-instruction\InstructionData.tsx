import {
	IconMapPin,
	IconUsers,
	IconBulb,
	IconStar,
	IconBriefcase,
	IconUser,
} from "@tabler/icons-react";
import type { videoDataType } from "../../types";

export const InstructionData = (videoType: videoDataType) => {
	switch (videoType) {
		case "EarlyLife":
			return {
				title: "Early Life Video – What to Share",
				description:
					"Share the story of your early years so viewers can understand your background and journey.",
				sections: [
					{
						title: "Background & Places",
						icon: <IconMapPin size="1.2rem" />,
						points: [
							"Where you were born and grew up.",
							"Cities you've lived in and your experiences there.",
						],
					},
					{
						title: "Family & Friends",
						icon: <IconUsers size="1.2rem" />,
						points: [
							"Brief intro to your family.",
							"Friends you had and fun activities together.",
						],
					},
					{
						title: "Interests & Education",
						icon: <IconBulb size="1.2rem" />,
						points: [
							"Hobbies or interests in childhood.",
							"Schools, colleges, or universities you attended (with timelines).",
						],
					},
					{
						title: "Extras",
						icon: <IconStar size="1.2rem" />,
						points: [
							"Any other details that feel natural to include.",
						],
					},
				],
			};

		case "ProfessionalLife":
			return {
				title: "Professional Life Video – What to Talk About",
				description:
					"Talk about your career path, focusing on past roles, experiences, and what you learned.",
				sections: [
					{
						title: "Journey Timeline",
						icon: <IconBriefcase size="1.2rem" />,
						points: [
							"Present your story in chronological order with an approximate timeline.",
							"How many years you have been in your professional journey.",
						],
					},
					{
						title: "Jobs & Roles",
						icon: <IconUser size="1.2rem" />,
						points: [
							"Talk about the different jobs or roles you’ve had in the past (like intern, employee, founder, owner, or freelancer).",
							"Be sure to mention your titles too (for example: analyst, manager, director). Don’t include your current role—focus only on your past experiences.",
						],
					},
					{
						title: "Organizations",
						icon: <IconMapPin size="1.2rem" />,
						points: [
							"Organisations you’ve worked with – your role, responsibilities, duration, and locations.",
						],
					},
					{
						title: "Key Learnings & Experiences",
						icon: <IconStar size="1.2rem" />,
						points: [
							"Key learnings from each role or phase of your career.",
							"Memorable experiences, challenges, or impactful incidents.",
						],
					},
				],
			};

		case "CurrentLife":
			return {
				title: "Current Life Video – What to Talk About",
				description:
					"Introduce yourself and talk about your current work and achievements.",
				sections: [
					{
						title: "Personal Introduction",
						icon: <IconUser size="1.2rem" />,
						points: [
							"Clearly state your full name and where you are currently based (city/country).",
							"Provide a short overview of yourself, covering both personal and professional aspects.",
						],
					},
					{
						title: "Current Role",
						icon: <IconBriefcase size="1.2rem" />,
						points: [
							"Name of your current organisation and your role.",
							"Location of your work – city, country, or remote.",
							"When and how you began this role or journey.",
						],
					},
					{
						title: "Organisation Overview",
						icon: <IconMapPin size="1.2rem" />,
						points: [
							"A short description of what your organisation does and the problems it solves.",
							"Current status of the organisation – products, customers, revenue, team size, etc.",
						],
					},
					{
						title: "Achievements & Highlights",
						icon: <IconStar size="1.2rem" />,
						points: [
							"Notable facts, achievements, or recognitions about your organisation, startup, or team.",
						],
					},
				],
			};

		default:
			return {
				title: "Video Instructions",
				description:
					"Please record or upload your video following the relevant guidelines.",
				sections: [],
			};
	}
};
