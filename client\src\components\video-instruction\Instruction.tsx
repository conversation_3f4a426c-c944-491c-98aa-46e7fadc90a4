import { But<PERSON>, Stack, Text, List } from "@mantine/core";
import { useMemo } from "react";
import { InstructionData } from "./InstructionData";
import type { videoDataType } from "../../types";
import { IconPoint } from "@tabler/icons-react";

const Instruction = (props: {
	videoType: videoDataType;
	onClose?: () => void;
}) => {
	const instructions = useMemo(() => {
		return InstructionData(props.videoType);
	}, [props.videoType]);

	return (
		<>
			<Stack gap="xs">
				<Text c="gray.7">{instructions.description}</Text>

				{instructions.sections &&
					instructions.sections.map((section, index) => (
						<Stack key={index} gap="xs">
							<Text
								fw={600}
								style={{
									display: "flex",
									alignItems: "center",
									gap: "8px",
								}}
							>
								{section.icon}
								{section.title}
							</Text>
							<List
								spacing="1"
								size="sm"
								withPadding
								styles={{
									itemWrapper: {
										alignItems: "flex-start",
									},
								}}
								icon={<IconPoint size={"1rem"} />}
							>
								{section.points.map((point, pointIndex) => (
									<List.Item key={pointIndex}>
										{point}
									</List.Item>
								))}
							</List>
						</Stack>
					))}

				{props.onClose && (
					<Button onClick={props.onClose} fullWidth mt="md">
						Got it!
					</Button>
				)}
			</Stack>
		</>
	);
};

export default Instruction;
