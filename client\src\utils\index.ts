interface FallbackImageProps {
	firstName: string;
	lastName: string;
}

export function fallbackImage({
	firstName,
	lastName,
}: FallbackImageProps): string {
	if (
		!firstName ||
		!lastName ||
		/^\d/.test(firstName.charAt(0)) ||
		/^\d/.test(lastName.charAt(0))
	) {
		return `https://api.dicebear.com/5.x/initials/svg?seed=SM`;
	}

	return `https://api.dicebear.com/5.x/initials/svg?seed=${firstName.charAt(0)}${lastName.charAt(0)}`;
}
