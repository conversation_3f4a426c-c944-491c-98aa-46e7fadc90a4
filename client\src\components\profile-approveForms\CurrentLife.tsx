import React, { useState } from "react";
import CurrentLifePreview from "../profile/CurrentLifePreview";
import CurrentLifeForm from "../onBoarding-forms/CurrentLifeForm";
import type { CurrentLifeDataType } from "../../types";

interface CurrentLifeProps {
	currentLifeData: CurrentLifeDataType;
	fetchProfile: () => void;
	userId?: string;
}

const CurrentLife: React.FC<CurrentLifeProps> = ({
	currentLifeData,
	fetchProfile,
	userId,
}) => {
	const [editing, setEditing] = useState(false);
	return (
		<>
			{editing ? (
				<>
					<CurrentLifeForm
						fetchProfile={fetchProfile}
						editing={editing}
						setEditing={setEditing}
						lifeData={currentLifeData}
						userId={userId}
					/>
				</>
			) : (
				<CurrentLifePreview
					showEdit={true}
					setEditing={setEditing}
					lifeData={currentLifeData}
				/>
			)}
		</>
	);
};

export default CurrentLife;
