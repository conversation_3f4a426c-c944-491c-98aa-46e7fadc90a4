import { Center, Title, Text, But<PERSON>, Stack } from "@mantine/core";
import { useNavigate } from "react-router-dom";

export default function NotFound() {
	const navigate = useNavigate();

	return (
		<Center style={{ height: "100vh" }}>
			<Stack align="center">
				<Title order={1} size={120} fw={900}>
					404
				</Title>
				<Text size="xl" fw={700}>
					Page Not Found
				</Text>
				<Text size="md" ta="center">
					The page you are looking for does not exist or has been
					moved.
				</Text>
				<Button
					variant="filled"
					size="md"
					onClick={() => navigate("/")}
				>
					Go to Home
				</Button>
			</Stack>
		</Center>
	);
}
