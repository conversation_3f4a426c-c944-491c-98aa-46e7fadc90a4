import { Con<PERSON><PERSON>, <PERSON>lex, <PERSON>, Ta<PERSON>, <PERSON><PERSON>, Indicator } from "@mantine/core";
import {
	IconBriefcase,
	IconClock,
	IconHome,
	IconUser,
	IconX,
} from "@tabler/icons-react";
import EarlyLife from "../components/profile-approveForms/EarlyLife";
import ProfessionalLife from "../components/profile-approveForms/ProfessionalLife";
import CurrentLife from "../components/profile-approveForms/CurrentLife";
import apiClient from "../config/axios";
import type { previewApprovedResponseDataType } from "../types";
import { useEffect, useState } from "react";
import { AxiosError } from "axios";
import { notifications } from "@mantine/notifications";
import { useNavigate, useParams } from "react-router-dom";
import { UserCheck } from "lucide-react";
import FullScreenLoader from "../components/FullScreenLoader";
import Profile from "./Profile";
import AssignRefererCuratorModal from "../components/modals/AssignRefererCuratorModal";

const ProfileReview = () => {
	const { userId } = useParams();
	const navigate = useNavigate();
	const [userData, setUserData] =
		useState<previewApprovedResponseDataType | null>(null);
	const [loading, setLoading] = useState<boolean>(false);
	const [activeTab, setActiveTab] = useState<string | null>("profile-info");
	const [openApprovedModal, setOpenApproveModal] = useState<boolean>(false);

	const fetchData = async () => {
		try {
			setLoading(true);
			const res = await apiClient.get<previewApprovedResponseDataType>(
				`/api/users/review-user/${userId}?view=profileReviewView`
			);
			setUserData(res.data);
		} catch (err) {
			if (err instanceof AxiosError) {
				notifications.show({
					title: "Error",
					message: "Unable to fetch Profile Data",
					color: "red",
					icon: <IconX />,
				});
				navigate("/pending-profiles");
			} else {
				console.error(err);
			}
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchData();
	}, []);

	if (loading || !userData) {
		return <FullScreenLoader />;
	}

	return (
		<Container p="lg" mb="xl">
			<Flex justify="space-between" align="center">
				<Title>Profile Review</Title>
				<Flex gap={12}>
					<Button
						color="green"
						leftSection={<UserCheck size={16} />}
						onClick={() => setOpenApproveModal(true)}
					>
						{userData.profileStatus === "re-approved"
							? "Re-Approve & Publish Profile"
							: "Approve & Publish Profile"}
					</Button>
				</Flex>
			</Flex>

			<Tabs
				defaultValue={activeTab}
				onChange={setActiveTab}
				variant="default"
				radius="md"
				mt="md"
			>
				<Tabs.List>
					<Tabs.Tab
						value="profile-info"
						leftSection={<IconUser size={16} />}
					>
						Profile Info
					</Tabs.Tab>
					<Tabs.Tab
						value="early-life"
						leftSection={<IconClock size={16} />}
					>
						{userData?.updatedEarlyLifeData ? (
							<Indicator
								size={8}
								color="red"
								offset={-4}
								position="top-end"
							>
								<> Early Life</>
							</Indicator>
						) : (
							<> Early Life</>
						)}
					</Tabs.Tab>
					<Tabs.Tab
						value="professional-life"
						leftSection={<IconBriefcase size={16} />}
					>
						{userData?.updatedProfessionalLifeData ? (
							<Indicator
								size={8}
								color="red"
								offset={-4}
								position="top-end"
							>
								<>Professional Life</>
							</Indicator>
						) : (
							<>Professional Life</>
						)}
					</Tabs.Tab>
					<Tabs.Tab
						value="current-life"
						leftSection={<IconHome size={16} />}
					>
						{userData?.updatedCurrentLifeData ? (
							<Indicator
								size={8}
								color="red"
								offset={-4}
								position="top-end"
							>
								<>Current Life</>
							</Indicator>
						) : (
							<>Current Life</>
						)}
					</Tabs.Tab>
				</Tabs.List>

				<Tabs.Panel value="profile-info" pt="lg">
					<Profile
						initialValues={{
							firstName: userData.basicDetails.firstName || "",
							middleName: userData.basicDetails.middleName || "",
							secondName: userData.basicDetails.secondName || "",
							email: userData.basicDetails.email,
							image: userData.basicDetails.image ?? null,
							mobile: userData.basicDetails.mobile,
							role: userData.basicDetails.role,
							address: userData.basicDetails.address || "",
							city: userData.basicDetails.city || "",
							currentOrganization:
								userData.basicDetails.currentOrganization || "",
							introduction:
								userData.basicDetails.introduction || "",
							quote: userData.basicDetails.quote || "",
							joy: userData.basicDetails.joy || "",
							twitter: userData.basicDetails.twitter || "",
							instagram: userData.basicDetails.instagram || "",
							linkedIn: userData.basicDetails.linkedIn || "",
							contentLinks:
								userData.basicDetails.contentLinks || [],
							otherSocialHandles:
								userData.basicDetails.otherSocialHandles || [],
						}}
						saveUrl={`/api/users/update-profile/${userId}`}
						fetchProfile={fetchData}
					/>
				</Tabs.Panel>
				<Tabs.Panel value="early-life" pt="lg">
					<EarlyLife
						fetchProfile={fetchData}
						earlyLifeData={userData.earlyLifeData}
						userId={userId}
					/>
				</Tabs.Panel>
				<Tabs.Panel value="professional-life" pt="lg">
					<ProfessionalLife
						fetchProfile={fetchData}
						professionalLifeData={userData.professionalLifeData}
						updatedProfessionalLifeData={
							userData.updatedProfessionalLifeData
						}
						userId={userId}
					/>
				</Tabs.Panel>
				<Tabs.Panel value="current-life" pt="lg">
					<CurrentLife
						fetchProfile={fetchData}
						currentLifeData={userData.currentLifeData}
						userId={userId}
					/>
				</Tabs.Panel>
			</Tabs>

			{openApprovedModal && (
				<AssignRefererCuratorModal
					opened={openApprovedModal}
					onClose={() => setOpenApproveModal(false)}
					userId={userId}
					onConfirm={() => navigate("/pending-profiles")}
					isReApproved={userData.profileStatus === "re-approved"}
				/>
			)}
		</Container>
	);
};

export default ProfileReview;
