import React, { useState } from "react";
import ProfessionalLifePreview from "../profile/ProfessionalLifePreview";
import type { ProfessionalLifeDataType } from "../../types";
import ProfessionalLifeForm from "../onBoarding-forms/ProfessionalLifeForm";

interface ProfessionalLifeProps {
	professionalLifeData: ProfessionalLifeDataType;
	fetchProfile: () => void;
	userId?: string;
	updatedProfessionalLifeData?: ProfessionalLifeDataType;
}

const ProfessionalLife: React.FC<ProfessionalLifeProps> = ({
	professionalLifeData,
	fetchProfile,
	userId,
	updatedProfessionalLifeData,
}) => {
	const [editing, setEditing] = useState(false);

	return (
		<>
			{editing ? (
				<>
					<ProfessionalLifeForm
						fetchProfile={fetchProfile}
						editing={editing}
						setEditing={setEditing}
						lifeData={professionalLifeData}
						userId={userId}
					/>
				</>
			) : (
				<ProfessionalLifePreview
					showEdit={true}
					setEditing={setEditing}
					lifeData={professionalLifeData}
					userId={userId}
					updatedLifeData={updatedProfessionalLifeData}
				/>
			)}
		</>
	);
};

export default ProfessionalLife;
