import User from "../models/User.js";
import { APP_CONFIG } from "../config/env.js";
import { decodeJWTToken } from "../utils/decodedJwt.js";
import { ONBOARDING_STEP, rolesValues } from "../constants/index.js";

export const checkLogin = async (req, res, next) => {
	try {
		const token = req.header("Authorization")?.replace("Bearer ", "");

		if (!token) {
			throw new Error("No token provided");
		}

		const decoded = decodeJWTToken(token, APP_CONFIG.ACCESS_TOKEN_SECRET);
		if (!decoded.verified) {
			throw new Error("Invalid token");
		}

		const user = await User.findById(decoded.userId);

		if (!user || !user.displayStatus) {
			throw new Error("User not found");
		}

		req.user = user;
		req.token = token;
		next();
	} catch (error) {
		res.status(401).json({
			error: "Unauthorized",
			message: error.message,
		});
		console.error(error);
	}
};

// Check if user has completed onboarding
export const checkOnboardingStatus = (req, res, next) => {
	try {
		// This middleware should run AFTER protectRoute, so req.user will be available
		if (!req.user) {
			return res
				.status(401)
				.json({ message: "Authentication required." });
		}

		if (
			req.user.onboardingStep !== ONBOARDING_STEP.COMPLETED &&
			!req.user.isAdminPanelUser
		) {
			return res.status(403).json({
				message:
					"Access denied. Please complete your profile onboarding.",
				onboardingStep: req.user.onboardingStep,
			});
		}

		next();
	} catch (error) {
		console.error(
			"Error in checkOnboardingStatus middleware: ",
			error.message
		);
		res.status(500).json({
			message: "Server error while checking onboarding status.",
		});
	}
};

export const checkAdminPanelView = (req, res, next) => {
	try {
		if (!req.user) {
			return res
				.status(401)
				.json({ message: "Authentication required." });
		}

		if (
			req.user.role !== rolesValues.Admin &&
			req.user.role !== rolesValues.SuperAdmin
		) {
			return res.status(403).json({
				message:
					"Access denied. You are not Authorized to change admin panel view.",
			});
		}

		next();
	} catch (error) {
		console.error(
			"Error in checkAdminPanelView middleware: ",
			error.message
		);
		res.status(500).json({
			message: "Server error while checking admin panel view.",
		});
	}
};
