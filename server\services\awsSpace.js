import {
	S3Client,
	PutObjectCommand,
	GetObjectCommand,
	ListObjectsV2Command,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import fs from "fs";
import { APP_CONFIG } from "../config/env.js";

const s3Client = new S3Client({
	forcePathStyle: true,
	region: APP_CONFIG.S3_REGION,
	credentials: {
		accessKeyId: APP_CONFIG.S3_ACCESS_KEY,
		secretAccessKey: APP_CONFIG.S3_SECRET_ACCESS_KEY,
	},
});

/**
 * Generates a pre-signed URL for uploading a file to S3-compatible storage.
 * @param {string} key The key (filename) of the object to upload.
 * @param {string} contentType The MIME type of the file to upload.
 * @returns {Promise<string>} A promise that resolves to the pre-signed URL.
 */

export const getSignedUrlForUpload = async (key, contentType) => {
	const command = new PutObjectCommand({
		Bucket: APP_CONFIG.S3_BUCKET,
		Key: key,
		ContentType: contentType,
	});

	try {
		const signedUrl = await getSignedUrl(s3Client, command, {
			expiresIn: 10 * 60,
		});
		return signedUrl;
	} catch (error) {
		console.error("Error generating signed URL:", error);
		throw new Error("Could not generate signed URL for upload.");
	}
};

export const getSignedUrlForView = async key => {
	const command = new GetObjectCommand({
		Bucket: APP_CONFIG.S3_BUCKET,
		Key: key,
	});

	try {
		const signedUrl = await getSignedUrl(s3Client, command, {});
		return signedUrl;
	} catch (error) {
		console.error("Error generating signed URL:", error);
		throw new Error("Could not generate signed URL for upload.");
	}
};

export const getPrefixPathForOnboardingVideo = (userId, videoId) => {
	const prefix = `SM360/onboarding/${userId}/${videoId}/`;
	return prefix;
};

export const listObjects = async prefix => {
	const command = new ListObjectsV2Command({
		Bucket: APP_CONFIG.S3_BUCKET,
		Prefix: prefix,
	});

	return s3Client.send(command).then(data => {
		return data.Contents;
	});
};

export const getPreviewVideoUrl = async (userId, videoId) => {
	let tempVideoPath = null;
	if (userId && videoId) {
		console.log(`Get the prefix path for video ${videoId}`);
		const pathToCheck = getPrefixPathForOnboardingVideo(userId, videoId);
		const files = await listObjects(pathToCheck);
		const filePath = files?.[0].Key;
		const signedUrl = await getSignedUrlForView(filePath);
		if (signedUrl) {
			tempVideoPath = signedUrl;
		}
	}
	return tempVideoPath;
};

/**
 * Downloads a file from S3.
 * @param {string} key The key (filename) of the object to download.
 * @param {string} downloadPath The local path to save the downloaded file.
 * @returns {Promise<void>}
 */
export const downloadFromS3 = async (key, downloadPath) => {
	const command = new GetObjectCommand({
		Bucket: APP_CONFIG.S3_BUCKET,
		Key: key,
	});

	try {
		const { Body } = await s3Client.send(command);
		if (!Body || typeof Body.pipe !== "function") {
			throw new Error("Invalid S3 object Body.");
		}
		const writeStream = fs.createWriteStream(downloadPath);
		await new Promise((resolve, reject) => {
			Body.pipe(writeStream);
			Body.on("error", reject);
			writeStream.on("finish", resolve);
		});
	} catch (error) {
		console.error("Error downloading from S3:", error);
		throw new Error("Could not download file from S3.");
	}
};
