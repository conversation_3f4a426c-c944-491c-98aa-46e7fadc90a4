import { <PERSON>, <PERSON><PERSON>, Badge } from "@mantine/core";
import { IconUser } from "@tabler/icons-react";
import { profileStatusColorMap, roleLabels } from "../../constants";
import type { User } from "../../types";
import { useNavigate } from "react-router-dom";

interface UserTableprops {
	users: User[];
	currentUserRole: number;
}

const UserTable = ({ users }: UserTableprops) => {
	const navigate = useNavigate();

	const handleUserView = (userId: string) => () => {
		navigate(`/pending-profiles/user/${userId}`);
	};
	return (
		<Table highlightOnHover withTableBorder striped>
			<Table.Thead>
				<Table.Tr>
					<Table.Th>First Name</Table.Th>
					<Table.Th>Last Name</Table.Th>
					<Table.Th>Email</Table.Th>
					<Table.Th>Mobile</Table.Th>
					<Table.Th>Role</Table.Th>
					<Table.Th>Status</Table.Th>
					<Table.Th>Actions</Table.Th>
				</Table.Tr>
			</Table.Thead>
			<Table.Tbody>
				{users.length > 0 ? (
					users.map(user => (
						<Table.Tr key={user._id}>
							<Table.Td>{user.firstName}</Table.Td>
							<Table.Td>{user.secondName}</Table.Td>
							<Table.Td>{user.email}</Table.Td>
							<Table.Td>{user.mobile}</Table.Td>
							<Table.Td>
								{
									roleLabels[
										user.role as keyof typeof roleLabels
									]
								}
							</Table.Td>
							<Table.Td>
								<Badge
									color={
										profileStatusColorMap[
											user.profileStatus
										]
									}
									size="sm"
									radius="sm"
								>
									{user.profileStatus === "pending"
										? "Pending"
										: user.profileStatus === "re-approved"
											? "Re-Approval"
											: "-"}
								</Badge>
							</Table.Td>
							<Table.Td>
								<Button
									variant="outline"
									onClick={handleUserView(user._id)}
									leftSection={<IconUser size={14} />}
								>
									View
								</Button>
							</Table.Td>
						</Table.Tr>
					))
				) : (
					<Table.Tr>
						<Table.Td colSpan={6} style={{ textAlign: "center" }}>
							No Users Found.
						</Table.Td>
					</Table.Tr>
				)}
			</Table.Tbody>
		</Table>
	);
};

export default UserTable;
